diff --git a/app/code/Comave/OrderCoolingOff/Block/Adminhtml/Order/View/CoolingOffStatus.php b/app/code/Comave/OrderCoolingOff/Block/Adminhtml/Order/View/CoolingOffStatus.php
new file mode 100644
index 000000000..08211735b
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Block/Adminhtml/Order/View/CoolingOffStatus.php
@@ -0,0 +1,81 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Block\Adminhtml\Order\View;
+
+use Magento\Backend\Block\Template;
+use Magento\Backend\Block\Template\Context;
+use Magento\Framework\Registry;
+use Magento\Sales\Model\Order;
+use Comave\OrderCoolingOff\Service\ValidationService;
+
+/**
+ * Block for displaying cooling-off status in order view
+ */
+class CoolingOffStatus extends Template
+{
+    /**
+     * @param Context $context
+     * @param Registry $registry
+     * @param ValidationService $validationService
+     * @param array $data
+     */
+    public function __construct(
+        Context $context,
+        private readonly Registry $registry,
+        private readonly ValidationService $validationService,
+        array $data = []
+    ) {
+        parent::__construct($context, $data);
+    }
+
+    /**
+     * Get current order
+     *
+     * @return Order|null
+     */
+    public function getOrder(): ?Order
+    {
+        return $this->registry->registry('current_order');
+    }
+
+    /**
+     * Check if order status is "Cooling Off Period"
+     *
+     * @return bool
+     */
+    public function isCoolingOffStatus(): bool
+    {
+        $order = $this->getOrder();
+        return $order && $order->getStatus() === 'cooling_off';
+    }
+
+    /**
+     * Get cooling-off expiration date
+     *
+     * @return string|null
+     */
+    public function getCoolingOffExpires(): ?string
+    {
+        $order = $this->getOrder();
+        return $order ? $order->getData('cooling_off_until') : null;
+    }
+
+    /**
+     * Format cooling-off expiration date for display
+     *
+     * @param string $date
+     * @return string
+     */
+    public function formatExpirationDate(string $date): string
+    {
+        return $this->_localeDate->formatDateTime(
+            new \DateTime($date),
+            \IntlDateFormatter::MEDIUM,
+            \IntlDateFormatter::MEDIUM
+        );
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Cron/ProcessExpiredOrders.php b/app/code/Comave/OrderCoolingOff/Cron/ProcessExpiredOrders.php
new file mode 100644
index 000000000..fe6472b09
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Cron/ProcessExpiredOrders.php
@@ -0,0 +1,117 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Cron;
+
+use Comave\OrderCoolingOff\Service\ProcessorService;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
+use Magento\Framework\Stdlib\DateTime\DateTime;
+use Magento\Framework\Exception\NoSuchEntityException;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Cron job to process expired cooling-off orders every minute
+ */
+class ProcessExpiredOrders
+{
+    private ProcessorService $processorService;
+    private OrderRepositoryInterface $orderRepository;
+    private CollectionFactory $orderCollectionFactory;
+    private DateTime $dateTime;
+    private LoggerInterface $logger;
+
+    public function __construct(
+        ProcessorService $processorService,
+        OrderRepositoryInterface $orderRepository,
+        CollectionFactory $orderCollectionFactory,
+        DateTime $dateTime,
+        LoggerInterface $logger
+    ) {
+        $this->processorService = $processorService;
+        $this->orderRepository = $orderRepository;
+        $this->orderCollectionFactory = $orderCollectionFactory;
+        $this->dateTime = $dateTime;
+        $this->logger = $logger;
+    }
+
+    /**
+     * Process all expired cooling-off orders
+     *
+     * @return void
+     */
+    public function execute(): void
+    {
+        $this->logger->info('=== COOLING-OFF CRON JOB STARTED ===');
+
+        try {
+            $expiredOrders = $this->getExpiredCoolingOffOrders();
+            $processedCount = 0;
+
+            $this->logger->info('Found expired cooling-off orders', [
+                'count' => count($expiredOrders),
+                'current_time' => $this->dateTime->gmtDate()
+            ]);
+
+            if (empty($expiredOrders)) {
+                $this->logger->info('No expired cooling-off orders to process');
+                return;
+            }
+
+            foreach ($expiredOrders as $order) {
+                try {
+                    $this->processorService->transitionToAccepted($order);
+                    $this->orderRepository->save($order);
+                    $processedCount++;
+
+                    $this->logger->info(
+                        'Cooling-off order processed by cron job',
+                        [
+                            'order_id' => $order->getEntityId(),
+                            'increment_id' => $order->getIncrementId()
+                        ]
+                    );
+
+                } catch (\Exception $e) {
+                    $this->logger->error(
+                        'Failed to process cooling-off order',
+                        [
+                            'order_id' => $order->getEntityId(),
+                            'error' => $e->getMessage()
+                        ]
+                    );
+                }
+            }
+
+            if ($processedCount > 0) {
+                $this->logger->info("Processed {$processedCount} expired cooling-off orders");
+            }
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Cooling-off orders processing cron job failed',
+                [
+                    'error' => $e->getMessage()
+                ]
+            );
+        }
+    }
+
+    /**
+     * Get orders with expired cooling-off period
+     *
+     * @return \Magento\Sales\Model\Order[]
+     */
+    private function getExpiredCoolingOffOrders(): array
+    {
+        $collection = $this->orderCollectionFactory->create();
+        $collection->addFieldToFilter('status', 'cooling_off')
+                   ->addFieldToFilter('cooling_off_until', ['notnull' => true])
+                   ->addFieldToFilter('cooling_off_until', ['lt' => $this->dateTime->gmtDate()]);
+
+        return $collection->getItems();
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Model/Resolver/CoolingOffUntil.php b/app/code/Comave/OrderCoolingOff/Model/Resolver/CoolingOffUntil.php
new file mode 100644
index 000000000..6e51458f0
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Model/Resolver/CoolingOffUntil.php
@@ -0,0 +1,43 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Model\Resolver;
+
+use Magento\Framework\GraphQl\Config\Element\Field;
+use Magento\Framework\GraphQl\Query\ResolverInterface;
+use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
+use Magento\Sales\Api\Data\OrderInterface;
+
+/**
+ * Resolver for cooling_off_until field in CustomerOrder
+ */
+class CoolingOffUntil implements ResolverInterface
+{
+    /**
+     * @inheritdoc
+     */
+    public function resolve(
+        Field $field,
+        $context,
+        ResolveInfo $info,
+        array $value = null,
+        array $args = null
+    ) {
+        if (!isset($value['model']) || !($value['model'] instanceof OrderInterface)) {
+            return null;
+        }
+
+        /** @var OrderInterface $order */
+        $order = $value['model'];
+        
+        $extensionAttributes = $order->getExtensionAttributes();
+        if ($extensionAttributes && $extensionAttributes->getCoolingOffUntil()) {
+            return $extensionAttributes->getCoolingOffUntil();
+        }
+        
+        return $order->getData('cooling_off_until');
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Observer/OrderCancelAfterObserver.php b/app/code/Comave/OrderCoolingOff/Observer/OrderCancelAfterObserver.php
new file mode 100644
index 000000000..7d9fd143a
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Observer/OrderCancelAfterObserver.php
@@ -0,0 +1,205 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Observer;
+
+use Magento\Framework\Event\Observer;
+use Magento\Framework\Event\ObserverInterface;
+use Magento\Sales\Model\Order;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Comave\Sales\Service\Order\CancellationService;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Model\Order\CreditmemoFactory;
+use Magento\Sales\Api\CreditmemoManagementInterface;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Observer to handle cooling-off specific logic after order cancellation
+ */
+class OrderCancelAfterObserver implements ObserverInterface
+{
+    /**
+     * @param ValidationService $validationService
+     * @param CancellationService $cancellationService
+     * @param OrderRepositoryInterface $orderRepository
+     * @param CreditmemoFactory $creditmemoFactory
+     * @param CreditmemoManagementInterface $creditmemoManagement
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ValidationService $validationService,
+        private readonly CancellationService $cancellationService,
+        private readonly OrderRepositoryInterface $orderRepository,
+        private readonly CreditmemoFactory $creditmemoFactory,
+        private readonly CreditmemoManagementInterface $creditmemoManagement,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Handle order cancellation with cooling-off specific logic
+     *
+     * @param Observer $observer
+     * @return void
+     */
+    public function execute(Observer $observer): void
+    {
+        /** @var Order $order */
+        $order = $observer->getEvent()->getOrder();
+
+        if (!$order instanceof Order) {
+            return;
+        }
+
+        try {
+            if ($this->validationService->wasCancelledDuringCoolingOff($order)) {
+                $originalReason = $order->getData('original_cancellation_reason');
+                $isCancelledBySeller = ($originalReason === 'canceled_by_seller') ||
+                                     ($order->getStatus() === 'canceled_by_seller');
+                $cancelledBy = $isCancelledBySeller ? 'seller' : 'customer';
+
+
+
+                $this->validationService->allowTransition();
+
+                $originalReason = $order->getData('original_cancellation_reason');
+                $isCancelledBySeller = ($originalReason === 'canceled_by_seller') ||
+                                     ($order->getStatus() === 'canceled_by_seller');
+
+                if ($isCancelledBySeller) {
+                    $order->setStatus('canceled_by_seller');
+                    $comment = __('Order cancelled by seller during cooling-off period via global cancellation flow.');
+                    $status = 'canceled_by_seller';
+                } else {
+                    $order->setStatus('canceled_by_customer');
+                    $comment = __('Order cancelled by customer during cooling-off period via global cancellation flow.');
+                    $status = 'canceled_by_customer';
+                }
+
+                $order->addCommentToStatusHistory(
+                    (string) $comment,
+                    $status,
+                    false,
+                    true
+                );
+
+                $this->orderRepository->save($order);
+
+                $this->processImmediateRefund($order);
+
+            }
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Error processing cooling-off cancellation logic',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage(),
+                    'trace' => $e->getTraceAsString()
+                ]
+            );
+            
+        }
+    }
+
+    /**
+     * Process immediate online refund for cooling-off cancellations
+     *
+     * @param Order $order
+     * @return void
+     */
+    private function processImmediateRefund(Order $order): void
+    {
+
+        try {
+            if (!$order->hasInvoices() || $order->getPayment()->getAmountPaid() <= 0) {
+                $this->logger->info(
+                    'No refund needed - order has no invoices or no payment made',
+                    ['order_id' => $order->getEntityId()]
+                );
+                return;
+            }
+
+            $originalState = $order->getState();
+            $originalStatus = $order->getStatus();
+
+            if (!$order->canCreditmemo()) {
+                $order->setState(Order::STATE_PROCESSING);
+                $order->setStatus('processing');
+            }
+
+            if (!$order->canCreditmemo()) {
+                $this->logger->warning(
+                    'Order still cannot be refunded even after state change',
+                    ['order_id' => $order->getEntityId()]
+                );
+                return;
+            }
+
+            $invoices = $order->getInvoiceCollection();
+            $invoices->setOrder('created_at', 'DESC');
+            $invoice = $invoices->getFirstItem();
+
+            if (!$invoice || !$invoice->getEntityId()) {
+                $this->logger->warning(
+                    'No valid invoice found for refund',
+                    ['order_id' => $order->getEntityId()]
+                );
+                return;
+            }
+
+            $creditmemo = $this->creditmemoFactory->createByInvoice($invoice);
+            if (!$creditmemo) {
+                $this->logger->error(
+                    'Failed to create credit memo',
+                    ['order_id' => $order->getEntityId()]
+                );
+                return;
+            }
+
+            $creditmemo->addComment(
+                __('Automatic online refund due to cooling-off period cancellation'),
+                false,
+                true
+            );
+
+            $creditmemo->setDoTransaction(true);
+
+            $this->creditmemoManagement->refund($creditmemo, false);
+
+            $order->setState($originalState);
+            $order->setStatus($originalStatus);
+            $this->orderRepository->save($order);
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to process immediate refund for cooling-off cancellation',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage(),
+                    'trace' => $e->getTraceAsString()
+                ]
+            );
+
+            $this->logger->info(
+                'Falling back to standard refund service',
+                ['order_id' => $order->getEntityId()]
+            );
+
+            try {
+                $this->cancellationService->processRefundIfNeeded($order);
+            } catch (\Exception $fallbackException) {
+                $this->logger->error(
+                    'Fallback refund also failed',
+                    [
+                        'order_id' => $order->getEntityId(),
+                        'fallback_error' => $fallbackException->getMessage()
+                    ]
+                );
+            }
+        }
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Observer/SetCoolingOffStatus.php b/app/code/Comave/OrderCoolingOff/Observer/SetCoolingOffStatus.php
new file mode 100644
index 000000000..9771a2b2e
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Observer/SetCoolingOffStatus.php
@@ -0,0 +1,113 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Observer;
+
+use Magento\Framework\Event\Observer;
+use Magento\Framework\Event\ObserverInterface;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Model\Order;
+use Magento\Framework\Stdlib\DateTime\DateTime;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Comave\OrderCoolingOff\Service\ConfigProvider;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Observer to set cooling-off status on order placement
+ */
+class SetCoolingOffStatus implements ObserverInterface
+{
+    /**
+     * @param ValidationService $validationService
+     * @param ConfigProvider $configProvider
+     * @param DateTime $dateTime
+     * @param LoggerInterface $logger
+     * @param OrderRepositoryInterface $orderRepository
+     */
+    public function __construct(
+        private readonly ValidationService $validationService,
+        private readonly ConfigProvider $configProvider,
+        private readonly DateTime $dateTime,
+        private readonly LoggerInterface $logger,
+        private readonly OrderRepositoryInterface $orderRepository
+    ) {
+    }
+
+    /**
+     * Set cooling-off status when order is placed
+     *
+     * @param Observer $observer
+     * @return void
+     */
+    public function execute(Observer $observer): void
+    {
+        /** @var Order $order */
+        $order = $observer->getEvent()->getOrder();
+
+        if (!$order instanceof Order) {
+            return;
+        }
+
+        if (!$this->validationService->shouldApplyCoolingOff($order)) {
+            $this->logger->info(
+                'Cooling-off period not applicable for order',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'reason' => 'validation_failed'
+                ]
+            );
+            return;
+        }
+
+        try {
+            $this->applyCoolingOffPeriod($order);
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to apply cooling-off period',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'increment_id' => $order->getIncrementId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+        }
+    }
+
+    /**
+     * Apply cooling-off period to order
+     *
+     * @param Order $order
+     * @return void
+     */
+    private function applyCoolingOffPeriod(Order $order): void
+    {
+        $durationMinutes = $this->configProvider->getDurationMinutes((int) $order->getStoreId());
+        
+        $coolingOffUntil = new \DateTime();
+        $coolingOffUntil->add(new \DateInterval("PT{$durationMinutes}M"));
+
+        $currentStatus = $order->getStatus();
+        $currentState = $order->getState();
+
+        $order->setStatus('cooling_off')
+              ->setState(Order::STATE_NEW)
+              ->setData('cooling_off_until', $coolingOffUntil->format('Y-m-d H:i:s'))
+              ->setData('is_cooling_off_applied', 1)
+              ->setData('intended_status_after_cooling_off', $currentStatus)
+              ->setData('intended_state_after_cooling_off', $currentState);
+
+        $order->addCommentToStatusHistory(
+            __('Order placed with %1 minute cooling-off period. Customer can cancel until %2.', 
+               $durationMinutes, 
+               $coolingOffUntil->format('Y-m-d H:i:s')
+            ),
+            'cooling_off',
+            false
+        );
+
+        $this->orderRepository->save($order);
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Marketplace/HideCoolingOffOrders.php b/app/code/Comave/OrderCoolingOff/Plugin/Marketplace/HideCoolingOffOrders.php
new file mode 100644
index 000000000..f5a90432d
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Marketplace/HideCoolingOffOrders.php
@@ -0,0 +1,94 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Marketplace;
+
+use Webkul\Marketplace\Model\Orders;
+use Magento\Framework\App\RequestInterface;
+use Magento\Customer\Model\Session as CustomerSession;
+use Comave\OrderCoolingOff\Service\ConfigProvider;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to hide cooling-off orders from seller dashboard
+ */
+class HideCoolingOffOrders
+{
+    /**
+     * @param RequestInterface $request
+     * @param CustomerSession $customerSession
+     * @param ConfigProvider $configProvider
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly RequestInterface $request,
+        private readonly CustomerSession $customerSession,
+        private readonly ConfigProvider $configProvider,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Hide cooling-off orders from seller collection
+     *
+     * @param Orders $subject
+     * @param \Magento\Sales\Model\ResourceModel\Order\Collection $result
+     * @return \Magento\Sales\Model\ResourceModel\Order\Collection
+     */
+    public function afterGetCollection(Orders $subject, $result)
+    {
+        if (!$this->isSellerContext()) {
+            return $result;
+        }
+
+        if (!$this->configProvider->isEnabled()) {
+            return $result;
+        }
+
+        try {
+            $result->addFieldToFilter('status', ['neq' => 'cooling_off']);
+
+            $this->logger->debug(
+                'Applied cooling-off filter to seller orders collection',
+                [
+                    'seller_id' => $this->customerSession->getCustomerId(),
+                    'collection_size_after_filter' => $result->getSize()
+                ]
+            );
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to apply cooling-off filter to seller orders',
+                [
+                    'error' => $e->getMessage(),
+                    'seller_id' => $this->customerSession->getCustomerId()
+                ]
+            );
+        }
+
+        return $result;
+    }
+
+    /**
+     * Check if current context is seller dashboard
+     *
+     * @return bool
+     */
+    private function isSellerContext(): bool
+    {
+        $moduleName = $this->request->getModuleName();
+        $controllerName = $this->request->getControllerName();
+        
+        $isMarketplaceArea = $moduleName === 'marketplace' || 
+                            str_contains($this->request->getRequestUri(), '/marketplace/');
+        
+        $isOrdersController = $controllerName === 'order' || 
+                             str_contains($this->request->getRequestUri(), '/order/');
+
+        $isSellerLoggedIn = $this->customerSession->isLoggedIn();
+
+        return $isMarketplaceArea && $isOrdersController && $isSellerLoggedIn;
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/AddCoolingOffExtensionAttributes.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/AddCoolingOffExtensionAttributes.php
new file mode 100644
index 000000000..e99cf2459
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/AddCoolingOffExtensionAttributes.php
@@ -0,0 +1,70 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Api\Data\OrderInterface;
+use Magento\Sales\Api\Data\OrderExtensionFactory;
+use Magento\Sales\Model\Order;
+use Comave\OrderCoolingOff\Service\ValidationService;
+
+/**
+ * Plugin to add cooling-off extension attributes to order
+ */
+class AddCoolingOffExtensionAttributes
+{
+    /**
+     * @param OrderExtensionFactory $orderExtensionFactory
+     * @param ValidationService $validationService
+     */
+    public function __construct(
+        private readonly OrderExtensionFactory $orderExtensionFactory,
+        private readonly ValidationService $validationService
+    ) {
+    }
+
+    /**
+     * Add cooling-off extension attributes after order load
+     *
+     * @param OrderInterface $order
+     * @return OrderInterface
+     */
+    public function afterLoad(OrderInterface $order): OrderInterface
+    {
+        $this->addCoolingOffData($order);
+        return $order;
+    }
+
+    /**
+     * Add cooling-off extension attributes after order get
+     *
+     * @param OrderInterface $order
+     * @return OrderInterface
+     */
+    public function afterGet(OrderInterface $order): OrderInterface
+    {
+        $this->addCoolingOffData($order);
+        return $order;
+    }
+
+    /**
+     * Add cooling-off data to order extension attributes
+     *
+     * @param OrderInterface $order
+     * @return void
+     */
+    private function addCoolingOffData(OrderInterface $order): void
+    {
+        $extensionAttributes = $order->getExtensionAttributes();
+        if ($extensionAttributes === null) {
+            $extensionAttributes = $this->orderExtensionFactory->create();
+        }
+
+        $extensionAttributes->setCoolingOffUntil($order->getData('cooling_off_until'));
+
+        $order->setExtensionAttributes($extensionAttributes);
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/CreditmemoManagementPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/CreditmemoManagementPlugin.php
new file mode 100644
index 000000000..ca0d95415
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/CreditmemoManagementPlugin.php
@@ -0,0 +1,51 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Api\CreditmemoManagementInterface;
+use Magento\Sales\Api\Data\CreditmemoInterface;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to force online refunds for cooling-off period cancellations
+ */
+class CreditmemoManagementPlugin
+{
+    private ValidationService $validationService;
+    private LoggerInterface $logger;
+
+    public function __construct(
+        ValidationService $validationService,
+        LoggerInterface $logger
+    ) {
+        $this->validationService = $validationService;
+        $this->logger = $logger;
+    }
+
+    /**
+     * Force online refunds for cooling-off cancellations
+     *
+     * @param CreditmemoManagementInterface $subject
+     * @param CreditmemoInterface $creditmemo
+     * @param bool $offlineRequested
+     * @return array
+     */
+    public function beforeRefund(
+        CreditmemoManagementInterface $subject,
+        CreditmemoInterface $creditmemo,
+        $offlineRequested = false
+    ): array {
+        $order = $creditmemo->getOrder();
+        if ($this->validationService->isInCoolingOffPeriod($order)) {
+            $offlineRequested = false;
+            $creditmemo->setDoTransaction(true);
+        }
+
+        return [$creditmemo, $offlineRequested];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCanCancelPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCanCancelPlugin.php
new file mode 100644
index 000000000..d8173fcba
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCanCancelPlugin.php
@@ -0,0 +1,44 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Model\Order;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to ensure cooling-off orders are always cancellable
+ */
+class OrderCanCancelPlugin
+{
+    private LoggerInterface $logger;
+
+    public function __construct(
+        LoggerInterface $logger
+    ) {
+        $this->logger = $logger;
+    }
+
+    /**
+     * Allow cancellation for orders in cooling-off status
+     *
+     * @param Order $subject
+     * @param bool $result
+     * @return bool
+     */
+    public function afterCanCancel(Order $subject, bool $result): bool
+    {
+        if ($result) {
+            return $result;
+        }
+
+        if ($subject->getStatus() === 'cooling_off') {
+            return true;
+        }
+
+        return $result;
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCancellationValidationPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCancellationValidationPlugin.php
new file mode 100644
index 000000000..f52cff557
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderCancellationValidationPlugin.php
@@ -0,0 +1,42 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Model\Order;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Magento\Framework\Exception\LocalizedException;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to validate cooling-off constraints before order cancellation
+ */
+class OrderCancellationValidationPlugin
+{
+    /**
+     * @param ValidationService $validationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ValidationService $validationService,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Validate cooling-off constraints before order cancellation
+     *
+     * @param Order $subject
+     * @return array
+     * @throws LocalizedException
+     */
+    public function beforeCancel(Order $subject): array
+    {
+
+
+        return [];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderHistoryPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderHistoryPlugin.php
new file mode 100644
index 000000000..651f0feec
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderHistoryPlugin.php
@@ -0,0 +1,70 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Magento\Sales\Model\Order;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to prevent intermediate status history entries during cooling-off period
+ */
+class OrderHistoryPlugin
+{
+    /**
+     * @param ValidationService $validationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ValidationService $validationService,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Show intermediate status messages with cooling_off status during cooling-off period
+     *
+     * @param Order $subject
+     * @param string $comment
+     * @param string|false $status
+     * @param bool $isCustomerNotified
+     * @param bool $isVisibleOnFront
+     * @return array
+     */
+    public function beforeAddCommentToStatusHistory(
+        Order $subject,
+        $comment,
+        $status = false,
+        $isCustomerNotified = false,
+        $isVisibleOnFront = false
+    ): array {
+
+
+        if ($status === 'cooling_off') {
+            return [$comment, $status, $isCustomerNotified, $isVisibleOnFront];
+        }
+
+        if ($this->validationService->isTransitionAllowed()) {
+            return [$comment, $status, $isCustomerNotified, $isVisibleOnFront];
+        }
+
+        $isInCoolingOff = $this->validationService->isInCoolingOffPeriod($subject);
+        $shouldApplyCoolingOff = false;
+
+        if (!$isInCoolingOff && !$subject->getEntityId()) {
+            $shouldApplyCoolingOff = $this->validationService->shouldApplyCoolingOff($subject);
+        }
+
+
+
+        if ($isInCoolingOff || $shouldApplyCoolingOff) {
+            return [$comment, 'cooling_off', $isCustomerNotified, $isVisibleOnFront];
+        }
+
+        return [$comment, $status, $isCustomerNotified, $isVisibleOnFront];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderRepositoryPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderRepositoryPlugin.php
new file mode 100644
index 000000000..2a5c8c0c4
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/OrderRepositoryPlugin.php
@@ -0,0 +1,98 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Api\Data\OrderSearchResultInterface;
+use Magento\Sales\Api\Data\OrderInterface;
+use Magento\Framework\Api\SearchCriteriaInterface;
+use Magento\Framework\App\RequestInterface;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to add cooling-off data to order repository operations
+ */
+class OrderRepositoryPlugin
+{
+    /**
+     * @param RequestInterface $request
+     * @param ValidationService $validationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly RequestInterface $request,
+        private readonly ValidationService $validationService,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Add cooling-off data after getting order list
+     *
+     * @param OrderRepositoryInterface $subject
+     * @param OrderSearchResultInterface $result
+     * @param SearchCriteriaInterface $searchCriteria
+     * @return OrderSearchResultInterface
+     */
+    public function afterGetList(
+        OrderRepositoryInterface $subject,
+        OrderSearchResultInterface $result,
+        SearchCriteriaInterface $searchCriteria
+    ): OrderSearchResultInterface {
+        foreach ($result->getItems() as $order) {
+            $this->addCoolingOffExtensionData($order);
+        }
+
+        return $result;
+    }
+
+    /**
+     * Add cooling-off data after getting single order
+     *
+     * @param OrderRepositoryInterface $subject
+     * @param OrderInterface $result
+     * @param int $id
+     * @return OrderInterface
+     */
+    public function afterGet(
+        OrderRepositoryInterface $subject,
+        OrderInterface $result,
+        int $id
+    ): OrderInterface {
+        $this->addCoolingOffExtensionData($result);
+        return $result;
+    }
+
+    /**
+     * Add cooling-off extension data to order
+     *
+     * @param OrderInterface $order
+     * @return void
+     */
+    private function addCoolingOffExtensionData(OrderInterface $order): void
+    {
+        try {
+            $extensionAttributes = $order->getExtensionAttributes();
+            if ($extensionAttributes === null) {
+                return;
+            }
+
+            $extensionAttributes->setCoolingOffUntil($order->getData('cooling_off_until'));
+
+            $order->setExtensionAttributes($extensionAttributes);
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to add cooling-off extension data to order',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+        }
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/PaymentRefundPlugin.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/PaymentRefundPlugin.php
new file mode 100644
index 000000000..7105d4746
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/PaymentRefundPlugin.php
@@ -0,0 +1,104 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Model\Order\Payment;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to force online refunds for cooling-off period cancellations
+ */
+class PaymentRefundPlugin
+{
+    private ValidationService $validationService;
+    private LoggerInterface $logger;
+    private OrderRepositoryInterface $orderRepository;
+
+    public function __construct(
+        ValidationService $validationService,
+        LoggerInterface $logger,
+        OrderRepositoryInterface $orderRepository
+    ) {
+        $this->validationService = $validationService;
+        $this->logger = $logger;
+        $this->orderRepository = $orderRepository;
+    }
+
+    /**
+     * Force online refunds for cooling-off cancellations
+     *
+     * @param Payment $subject
+     * @param $creditmemo
+     * @return array
+     */
+    public function beforeRefund(
+        Payment $subject,
+        $creditmemo
+    ): array {
+        $order = $subject->getOrder();
+
+
+
+        if ($this->validationService->wasCancelledDuringCoolingOff($order)) {
+            $originalReason = $order->getData('original_cancellation_reason');
+            $isCancelledBySeller = ($originalReason === 'canceled_by_seller') ||
+                                 ($order->getStatus() === 'canceled_by_seller');
+            $cancelledBy = $isCancelledBySeller ? 'seller' : 'customer';
+
+
+
+            $creditmemo->setDoTransaction(true);
+
+            try {
+                if ($isCancelledBySeller) {
+                    $order->setStatus('canceled_by_seller');
+                    $comment = __('Order cancelled by seller during cooling-off period.');
+                    $status = 'canceled_by_seller';
+                } else {
+                    $order->setStatus('canceled_by_customer');
+                    $comment = __('Order cancelled by customer during cooling-off period.');
+                    $status = 'canceled_by_customer';
+                }
+
+                $order->addCommentToStatusHistory(
+                    (string) $comment,
+                    $status,
+                    false,
+                    false
+                );
+
+                $this->orderRepository->save($order);
+
+
+            } catch (\Exception $e) {
+                $this->logger->error(
+                    'COOLING_OFF_PAYMENT_PLUGIN: Failed to set status before refund',
+                    [
+                        'order_id' => $order->getEntityId(),
+                        'error' => $e->getMessage()
+                    ]
+                );
+            }
+
+
+        } else {
+            $this->logger->info(
+                'COOLING_OFF_PAYMENT_PLUGIN: Order is NOT in cooling-off period - using original refund settings',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'increment_id' => $order->getIncrementId(),
+                    'creditmemo_do_transaction' => $creditmemo->getDoTransaction(),
+                    'cooling_off_until' => $order->getCoolingOffUntil()
+                ]
+            );
+        }
+
+        return [$creditmemo];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Sales/ProtectCoolingOffStatus.php b/app/code/Comave/OrderCoolingOff/Plugin/Sales/ProtectCoolingOffStatus.php
new file mode 100644
index 000000000..5271323fc
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Sales/ProtectCoolingOffStatus.php
@@ -0,0 +1,87 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Sales;
+
+use Magento\Sales\Model\Order;
+use Comave\OrderCoolingOff\Service\ValidationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Plugin to protect cooling-off orders from automatic status changes
+ */
+class ProtectCoolingOffStatus
+{
+    /**
+     * @param ValidationService $validationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ValidationService $validationService,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Prevent status changes during cooling-off period and store intended changes
+     *
+     * @param Order $subject
+     * @param string $status
+     * @return array
+     */
+    public function beforeSetStatus(Order $subject, string $status): array
+    {
+        if ($status === 'cooling_off') {
+            return [$status];
+        }
+
+        if ($this->validationService->isTransitionAllowed()) {
+            return [$status];
+        }
+
+        if ($this->validationService->isInCoolingOffPeriod($subject)) {
+            $currentIntendedStatus = $subject->getData('intended_status_after_cooling_off');
+
+            if ($currentIntendedStatus !== $status) {
+                $subject->setData('intended_status_after_cooling_off', $status);
+            }
+
+            return [$subject->getStatus()];
+        }
+
+        return [$status];
+    }
+
+    /**
+     * Prevent state changes during cooling-off period and store intended changes
+     *
+     * @param Order $subject
+     * @param string $state
+     * @return array
+     */
+    public function beforeSetState(Order $subject, string $state): array
+    {
+        if ($state === Order::STATE_NEW) {
+            return [$state];
+        }
+
+        if ($this->validationService->isTransitionAllowed()) {
+            return [$state];
+        }
+
+        if ($this->validationService->isInCoolingOffPeriod($subject)) {
+            $currentIntendedState = $subject->getData('intended_state_after_cooling_off');
+
+            if ($currentIntendedState !== $state) {
+                $subject->setData('intended_state_after_cooling_off', $state);
+            }
+
+            return [$subject->getState()];
+        }
+
+        return [$state];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Plugin/Ui/DataProvider/OrderGridDataProvider.php b/app/code/Comave/OrderCoolingOff/Plugin/Ui/DataProvider/OrderGridDataProvider.php
new file mode 100644
index 000000000..d2ad04f0f
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Plugin/Ui/DataProvider/OrderGridDataProvider.php
@@ -0,0 +1,55 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Plugin\Ui\DataProvider;
+
+use Magento\Sales\Ui\Component\DataProvider\Document;
+use Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory;
+
+/**
+ * Plugin to add cooling-off fields to order grid collection
+ */
+class OrderGridDataProvider
+{
+    /**
+     * Add cooling-off fields to order grid collection
+     *
+     * @param CollectionFactory $subject
+     * @param \Magento\Framework\Data\Collection $result
+     * @param string $requestName
+     * @return \Magento\Framework\Data\Collection
+     */
+    public function afterGetReport(
+        CollectionFactory $subject,
+        $result,
+        string $requestName
+    ) {
+        if ($requestName === 'sales_order_grid_data_source') {
+            if ($result instanceof \Magento\Framework\Data\Collection\AbstractDb) {
+                $select = $result->getSelect();
+                $fromPart = $select->getPart(\Magento\Framework\DB\Select::FROM);
+
+                if (!isset($fromPart['sales_order'])) {
+                    $select->joinLeft(
+                        ['sales_order_cooling' => $result->getTable('sales_order')],
+                        'main_table.entity_id = sales_order_cooling.entity_id',
+                        [
+                            'cooling_off_until' => 'sales_order_cooling.cooling_off_until',
+                            'is_cooling_off_applied' => 'sales_order_cooling.is_cooling_off_applied'
+                        ]
+                    );
+                } else {
+                    $select->columns([
+                        'cooling_off_until' => 'sales_order.cooling_off_until',
+                        'is_cooling_off_applied' => 'sales_order.is_cooling_off_applied'
+                    ]);
+                }
+            }
+        }
+
+        return $result;
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Service/ConfigProvider.php b/app/code/Comave/OrderCoolingOff/Service/ConfigProvider.php
new file mode 100644
index 000000000..c5940d6d4
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Service/ConfigProvider.php
@@ -0,0 +1,75 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Service;
+
+use Magento\Framework\App\Config\ScopeConfigInterface;
+use Magento\Store\Model\ScopeInterface;
+
+/**
+ * Configuration provider for cooling-off period functionality
+ */
+class ConfigProvider
+{
+    private const string XML_PATH_ENABLED = 'sales/cooling_off/enabled';
+    private const string XML_PATH_EXCLUDE_ADMIN = 'sales/cooling_off/exclude_admin_orders';
+
+    /**
+     * Fixed cooling-off duration in minutes (not configurable)
+     * SET TO 30 MINUTES
+     */
+    private const int COOLING_OFF_DURATION_MINUTES = 30;
+
+    /**
+     * @param ScopeConfigInterface $scopeConfig
+     */
+    public function __construct(
+        private readonly ScopeConfigInterface $scopeConfig
+    ) {
+    }
+
+    /**
+     * Check if cooling-off period is enabled
+     *
+     * @param int|null $storeId
+     * @return bool
+     */
+    public function isEnabled(?int $storeId = null): bool
+    {
+        return $this->scopeConfig->isSetFlag(
+            self::XML_PATH_ENABLED,
+            ScopeInterface::SCOPE_STORE,
+            $storeId
+        );
+    }
+
+    /**
+     * Get cooling-off duration in minutes
+     * Fixed at 30 minutes - not configurable
+     *
+     * @param int|null $storeId
+     * @return int
+     */
+    public function getDurationMinutes(?int $storeId = null): int
+    {
+        return self::COOLING_OFF_DURATION_MINUTES;
+    }
+
+    /**
+     * Check if admin orders should be excluded
+     *
+     * @param int|null $storeId
+     * @return bool
+     */
+    public function shouldExcludeAdminOrders(?int $storeId = null): bool
+    {
+        return $this->scopeConfig->isSetFlag(
+            self::XML_PATH_EXCLUDE_ADMIN,
+            ScopeInterface::SCOPE_STORE,
+            $storeId
+        );
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Service/ProcessorService.php b/app/code/Comave/OrderCoolingOff/Service/ProcessorService.php
new file mode 100644
index 000000000..7824e8880
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Service/ProcessorService.php
@@ -0,0 +1,109 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Service;
+
+use Magento\Sales\Model\Order;
+use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
+use Comave\Sales\Service\Order\CancellationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Processor service for cooling-off period operations
+ */
+class ProcessorService
+{
+    /**
+     * @param EventManagerInterface $eventManager
+     * @param CancellationService $cancellationService
+     * @param ValidationService $validationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly EventManagerInterface $eventManager,
+        private readonly CancellationService $cancellationService,
+        private readonly ValidationService $validationService,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Transition order from cooling-off to accepted status
+     *
+     * @param Order $order
+     * @return void
+     */
+    public function transitionToAccepted(Order $order): void
+    {
+        $this->validationService->allowTransition();
+
+        try {
+            $intendedStatus = $order->getData('intended_status_after_cooling_off');
+            $intendedState = $order->getData('intended_state_after_cooling_off');
+
+            $order->setStatus($intendedStatus)
+                  ->setState($intendedState);
+
+            $order->addCommentToStatusHistory(
+                __('Cooling-off period expired. Order moved to %1.', $intendedStatus),
+                $intendedStatus,
+                false
+            );
+
+            $order->unsetData('intended_status_after_cooling_off');
+            $order->unsetData('intended_state_after_cooling_off');
+
+            $this->eventManager->dispatch(
+                'comave_order_cooling_off_expired',
+                [
+                    'order' => $order,
+                    'previous_status' => 'cooling_off',
+                    'new_status' => $intendedStatus
+                ]
+            );
+
+        } finally {
+            $this->validationService->disallowTransition();
+        }
+    }
+
+    /**
+     * Cancel order during cooling-off period
+     *
+     * @param Order $order
+     * @param string $reason
+     * @return void
+     */
+    public function cancelDuringCoolingOff(Order $order, string $reason = ''): void
+    {
+
+
+        $this->validationService->allowTransition();
+
+        try {
+            $comment = $reason
+                ? __('Order cancelled by customer during cooling-off period. Reason: %1', $reason)
+                : __('Order cancelled by customer during cooling-off period.');
+
+            $this->cancellationService->cancelOrderWithReason(
+                $order,
+                'canceled_by_customer',
+                (string) $comment
+            );
+
+            $this->eventManager->dispatch(
+                'comave_order_cooling_off_cancelled',
+                [
+                    'order' => $order,
+                    'reason' => $reason
+                ]
+            );
+
+
+        } finally {
+            $this->validationService->disallowTransition();
+        }
+    }
diff --git a/app/code/Comave/OrderCoolingOff/Service/ValidationService.php b/app/code/Comave/OrderCoolingOff/Service/ValidationService.php
new file mode 100644
index 000000000..6d993ed3c
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Service/ValidationService.php
@@ -0,0 +1,154 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Service;
+
+use Magento\Sales\Model\Order;
+use Magento\Framework\Stdlib\DateTime\DateTime;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Validation service for cooling-off period functionality
+ */
+class ValidationService
+{
+    /**
+     * Flag to allow legitimate cooling-off transitions
+     */
+    private bool $allowTransition = false;
+
+    /**
+     * @param ConfigProvider $configProvider
+     * @param DateTime $dateTime
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ConfigProvider $configProvider,
+        private readonly DateTime $dateTime,
+        private readonly LoggerInterface $logger
+    ) {
+    }
+
+    /**
+     * Allow transition (used by ProcessorService)
+     *
+     * @return void
+     */
+    public function allowTransition(): void
+    {
+        $this->allowTransition = true;
+    }
+
+    /**
+     * Disallow transition
+     *
+     * @return void
+     */
+    public function disallowTransition(): void
+    {
+        $this->allowTransition = false;
+    }
+
+    /**
+     * Check if transition is allowed
+     *
+     * @return bool
+     */
+    public function isTransitionAllowed(): bool
+    {
+        return $this->allowTransition;
+    }
+
+    /**
+     * Check if cooling-off period should be applied to order
+     *
+     * @param Order $order
+     * @return bool
+     */
+    public function shouldApplyCoolingOff(Order $order): bool
+    {
+
+
+        if (!$this->configProvider->isEnabled((int) $order->getStoreId())) {
+            $this->logger->info('ValidationService: Cooling-off disabled in config', [
+                'order_id' => $order->getId(),
+                'store_id' => $order->getStoreId()
+            ]);
+            return false;
+        }
+
+        if ($this->configProvider->shouldExcludeAdminOrders((int) $order->getStoreId()) && $this->isAdminOrder($order)) {
+            $this->logger->info('ValidationService: Admin order excluded from cooling-off', [
+                'order_id' => $order->getId()
+            ]);
+            return false;
+        }
+
+        if (!in_array($order->getState(), [Order::STATE_NEW, Order::STATE_PENDING_PAYMENT, Order::STATE_PROCESSING], true)) {
+            $this->logger->info('ValidationService: Order state not valid for cooling-off', [
+                'order_id' => $order->getId(),
+                'current_state' => $order->getState(),
+                'valid_states' => [Order::STATE_NEW, Order::STATE_PENDING_PAYMENT, Order::STATE_PROCESSING]
+            ]);
+            return false;
+        }
+
+        return true;
+    }
+
+    /**
+     * Check if order is currently in cooling-off period
+     *
+     * @param Order $order
+     * @return bool
+     */
+    public function isInCoolingOffPeriod(Order $order): bool
+    {
+        $coolingOffUntil = $order->getData('cooling_off_until');
+        if (!$coolingOffUntil) {
+            return false;
+        }
+
+        $isWithinCoolingOffTime = $this->dateTime->gmtTimestamp() < strtotime($coolingOffUntil);
+        $isCurrentlyCoolingOff = $order->getStatus() === 'cooling_off';
+        $wasRecentlyCoolingOff = $isWithinCoolingOffTime && !empty($coolingOffUntil);
+
+        return $isWithinCoolingOffTime && ($isCurrentlyCoolingOff || $wasRecentlyCoolingOff);
+    }
+
+    /**
+     * Check if order was cancelled during cooling-off period
+     * This method is specifically for cancellation scenarios where the status might have changed
+     *
+     * @param Order $order
+     * @return bool
+     */
+    public function wasCancelledDuringCoolingOff(Order $order): bool
+    {
+        $coolingOffUntil = $order->getData('cooling_off_until');
+        if (!$coolingOffUntil) {
+            return false;
+        }
+
+        return $this->dateTime->gmtTimestamp() < strtotime($coolingOffUntil);
+    }
+
+
+
+
+    /**
+     * Check if order was placed by admin
+     *
+     * @param Order $order
+     * @return bool
+     */
+    private function isAdminOrder(Order $order): bool
+    {
+        return $order->getRemoteIp() === null ||
+               $order->getXForwardedFor() === null ||
+               !empty($order->getData('created_from_admin'));
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Setup/Patch/Data/CreateCoolingOffOrderStatuses.php b/app/code/Comave/OrderCoolingOff/Setup/Patch/Data/CreateCoolingOffOrderStatuses.php
new file mode 100644
index 000000000..625277506
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Setup/Patch/Data/CreateCoolingOffOrderStatuses.php
@@ -0,0 +1,115 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Setup\Patch\Data;
+
+use Magento\Framework\Setup\Patch\DataPatchInterface;
+use Magento\Framework\Setup\ModuleDataSetupInterface;
+use Magento\Sales\Model\Order;
+use Magento\Sales\Model\Order\Status;
+use Magento\Sales\Model\ResourceModel\Order\Status as StatusResource;
+use Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory as StatusCollectionFactory;
+
+/**
+ * Create cooling-off period order statuses
+ */
+class CreateCoolingOffOrderStatuses implements DataPatchInterface
+{
+    private const array STATUSES = [
+        'cooling_off' => [
+            'label' => 'Cooling Off Period',
+            'state' => Order::STATE_NEW
+        ]
+    ];
+
+    /**
+     * @param ModuleDataSetupInterface $moduleDataSetup
+     * @param StatusResource $statusResource
+     * @param StatusCollectionFactory $statusCollectionFactory
+     */
+    public function __construct(
+        private readonly ModuleDataSetupInterface $moduleDataSetup,
+        private readonly StatusResource $statusResource,
+        private readonly StatusCollectionFactory $statusCollectionFactory
+    ) {
+    }
+
+    /**
+     * Apply data patch
+     *
+     * @return void
+     */
+    public function apply(): void
+    {
+        $this->moduleDataSetup->getConnection()->startSetup();
+
+        foreach (self::STATUSES as $statusCode => $statusData) {
+            $this->createOrderStatus($statusCode, $statusData['label'], $statusData['state']);
+        }
+
+        $this->moduleDataSetup->getConnection()->endSetup();
+    }
+
+    /**
+     * Create order status and assign to state
+     *
+     * @param string $statusCode
+     * @param string $label
+     * @param string $state
+     * @return void
+     */
+    private function createOrderStatus(string $statusCode, string $label, string $state): void
+    {
+        $statusCollection = $this->statusCollectionFactory->create();
+        $statusCollection->addFieldToFilter('status', $statusCode);
+        
+        if ($statusCollection->getSize() > 0) {
+            return;
+        }
+
+        $statusData = [
+            'status' => $statusCode,
+            'label' => $label
+        ];
+
+        $this->moduleDataSetup->getConnection()->insert(
+            $this->moduleDataSetup->getTable('sales_order_status'),
+            $statusData
+        );
+
+        $statusStateData = [
+            'status' => $statusCode,
+            'state' => $state,
+            'is_default' => 0,
+            'visible_on_front' => 1
+        ];
+
+        $this->moduleDataSetup->getConnection()->insert(
+            $this->moduleDataSetup->getTable('sales_order_status_state'),
+            $statusStateData
+        );
+    }
+
+    /**
+     * Get dependencies
+     *
+     * @return array
+     */
+    public static function getDependencies(): array
+    {
+        return [];
+    }
+
+    /**
+     * Get aliases
+     *
+     * @return array
+     */
+    public function getAliases(): array
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Setup/Patch/Schema/AddOrderAttributesSchema.php b/app/code/Comave/OrderCoolingOff/Setup/Patch/Schema/AddOrderAttributesSchema.php
new file mode 100644
index 000000000..3e577d0f7
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Setup/Patch/Schema/AddOrderAttributesSchema.php
@@ -0,0 +1,84 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Setup\Patch\Schema;
+
+use Magento\Framework\App\ResourceConnection;
+use Magento\Framework\Setup\Patch\SchemaPatchInterface;
+use Magento\Framework\DB\Ddl\Table;
+
+/**
+ * Add cooling-off period columns to sales_order table
+ */
+class AddOrderAttributesSchema implements SchemaPatchInterface
+{
+    /**
+     * @param ResourceConnection $resourceConnection
+     */
+    public function __construct(
+        private readonly ResourceConnection $resourceConnection
+    ) {
+    }
+
+    /**
+     * Apply schema patch
+     *
+     * @return self
+     */
+    public function apply(): self
+    {
+        $connection = $this->resourceConnection->getConnection('write');
+        $tableName = $this->resourceConnection->getTableName('sales_order');
+
+        if (!$connection->tableColumnExists($tableName, 'cooling_off_until')) {
+            $connection->addColumn(
+                $tableName,
+                'cooling_off_until',
+                [
+                    'type' => Table::TYPE_DATETIME,
+                    'nullable' => true,
+                    'comment' => 'Date and time when cooling-off period expires'
+                ]
+            );
+        }
+
+        if (!$connection->tableColumnExists($tableName, 'is_cooling_off_applied')) {
+            $connection->addColumn(
+                $tableName,
+                'is_cooling_off_applied',
+                [
+                    'type' => Table::TYPE_SMALLINT,
+                    'size' => 1,
+                    'nullable' => false,
+                    'default' => 0,
+                    'comment' => 'Whether cooling-off period was applied to this order'
+                ]
+            );
+        }
+
+        return $this;
+    }
+
+    /**
+     * Get dependencies
+     *
+     * @return array
+     */
+    public static function getDependencies(): array
+    {
+        return [];
+    }
+
+    /**
+     * Get aliases
+     *
+     * @return array
+     */
+    public function getAliases(): array
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/Ui/Component/Listing/Column/CoolingOffExpires.php b/app/code/Comave/OrderCoolingOff/Ui/Component/Listing/Column/CoolingOffExpires.php
new file mode 100644
index 000000000..4dc7b3139
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/Ui/Component/Listing/Column/CoolingOffExpires.php
@@ -0,0 +1,78 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\OrderCoolingOff\Ui\Component\Listing\Column;
+
+use Magento\Framework\View\Element\UiComponent\ContextInterface;
+use Magento\Framework\View\Element\UiComponentFactory;
+use Magento\Ui\Component\Listing\Columns\Column;
+use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
+
+/**
+ * Cooling-off expiration column for order grid
+ */
+class CoolingOffExpires extends Column
+{
+    /**
+     * @param ContextInterface $context
+     * @param UiComponentFactory $uiComponentFactory
+     * @param TimezoneInterface $timezone
+     * @param array $components
+     * @param array $data
+     */
+    public function __construct(
+        ContextInterface $context,
+        UiComponentFactory $uiComponentFactory,
+        private readonly TimezoneInterface $timezone,
+        array $components = [],
+        array $data = []
+    ) {
+        parent::__construct($context, $uiComponentFactory, $components, $data);
+    }
+
+    /**
+     * Prepare Data Source
+     *
+     * @param array $dataSource
+     * @return array
+     */
+    public function prepareDataSource(array $dataSource): array
+    {
+        if (isset($dataSource['data']['items'])) {
+            foreach ($dataSource['data']['items'] as &$item) {
+                $item[$this->getData('name')] = $this->formatCoolingOffExpires($item);
+            }
+        }
+
+        return $dataSource;
+    }
+
+    /**
+     * Format cooling-off expiration for display
+     *
+     * @param array $item
+     * @return string
+     */
+    private function formatCoolingOffExpires(array $item): string
+    {
+        if (empty($item['cooling_off_until']) || empty($item['is_cooling_off_applied'])) {
+            return '';
+        }
+
+        try {
+            $expiresAt = new \DateTime($item['cooling_off_until']);
+            $formatted = $this->timezone->formatDateTime(
+                $expiresAt,
+                \IntlDateFormatter::SHORT,
+                \IntlDateFormatter::SHORT
+            );
+
+            return $formatted;
+        } catch (\Exception $e) {
+            return '';
+        }
+    }
+}
diff --git a/app/code/Comave/OrderCoolingOff/etc/acl.xml b/app/code/Comave/OrderCoolingOff/etc/acl.xml
new file mode 100644
index 000000000..527e6ba71
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/acl.xml
@@ -0,0 +1,27 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
+    <acl>
+        <resources>
+            <resource id="Magento_Backend::admin">
+                <resource id="Magento_Backend::stores">
+                    <resource id="Magento_Backend::stores_settings">
+                        <resource id="Magento_Config::config">
+                            <resource id="Comave_OrderCoolingOff::config" title="Order Cooling-Off Period Configuration"/>
+                        </resource>
+                    </resource>
+                </resource>
+                <resource id="Magento_Sales::sales">
+                    <resource id="Magento_Sales::sales_operation">
+                        <resource id="Comave_OrderCoolingOff::cooling_off_management" title="Cooling-Off Period Management"/>
+                    </resource>
+                </resource>
+            </resource>
+        </resources>
+    </acl>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/adminhtml/system.xml b/app/code/Comave/OrderCoolingOff/etc/adminhtml/system.xml
new file mode 100644
index 000000000..19d061540
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/adminhtml/system.xml
@@ -0,0 +1,32 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
+    <system>
+        <section id="sales">
+            <group id="cooling_off" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
+                <label>Order Cooling-Off Period</label>
+
+                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
+                    <label>Enable Cooling-Off Period</label>
+                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
+                    <comment>Enable 30-minute cooling-off period for new orders</comment>
+                </field>
+
+                <field id="exclude_admin_orders" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
+                    <label>Exclude Admin Orders</label>
+                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
+                    <comment>Exclude orders placed from admin panel from cooling-off period</comment>
+                    <depends>
+                        <field id="enabled">1</field>
+                    </depends>
+                </field>
+            </group>
+
+        </section>
+    </system>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/config.xml b/app/code/Comave/OrderCoolingOff/etc/config.xml
new file mode 100644
index 000000000..743c50089
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/config.xml
@@ -0,0 +1,23 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
+    <default>
+        <sales>
+            <cooling_off>
+                <enabled>0</enabled>
+                <exclude_admin_orders>0</exclude_admin_orders>
+            </cooling_off>
+            <order_statuses>
+                <cooling_off>
+                    <label>Cooling Off Period</label>
+                    <state>new</state>
+                </cooling_off>
+            </order_statuses>
+        </sales>
+    </default>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/crontab.xml b/app/code/Comave/OrderCoolingOff/etc/crontab.xml
new file mode 100644
index 000000000..e403b2ecc
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/crontab.xml
@@ -0,0 +1,16 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
+    <group id="default">
+        <job name="comave_cooling_off_process_expired"
+             instance="Comave\OrderCoolingOff\Cron\ProcessExpiredOrders"
+             method="execute">
+            <schedule>* * * * *</schedule>
+        </job>
+    </group>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/di.xml b/app/code/Comave/OrderCoolingOff/etc/di.xml
new file mode 100644
index 000000000..06e78b1cb
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/di.xml
@@ -0,0 +1,57 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
+
+    <type name="Magento\Sales\Api\Data\OrderInterface">
+        <plugin name="add_cooling_off_extension_attributes"
+                type="Comave\OrderCoolingOff\Plugin\Sales\AddCoolingOffExtensionAttributes"/>
+    </type>
+
+    <type name="Magento\Sales\Api\OrderRepositoryInterface">
+        <plugin name="add_cooling_off_data"
+                type="Comave\OrderCoolingOff\Plugin\Sales\OrderRepositoryPlugin"/>
+    </type>
+
+    <type name="Magento\Sales\Model\Order">
+        <plugin name="cooling_off_cancellation_validation"
+                type="Comave\OrderCoolingOff\Plugin\Sales\OrderCancellationValidationPlugin"
+                sortOrder="5"/>
+        <plugin name="cooling_off_can_cancel"
+                type="Comave\OrderCoolingOff\Plugin\Sales\OrderCanCancelPlugin"
+                sortOrder="10"/>
+        <plugin name="protect_cooling_off_status"
+                type="Comave\OrderCoolingOff\Plugin\Sales\ProtectCoolingOffStatus"
+                sortOrder="15"/>
+        <plugin name="block_intermediate_history"
+                type="Comave\OrderCoolingOff\Plugin\Sales\OrderHistoryPlugin"
+                sortOrder="20"/>
+    </type>
+
+    <type name="Magento\Sales\Api\CreditmemoManagementInterface">
+        <plugin name="cooling_off_force_online_refund"
+                type="Comave\OrderCoolingOff\Plugin\Sales\CreditmemoManagementPlugin"
+                sortOrder="5"/>
+    </type>
+
+    <type name="Magento\Sales\Model\Order\Payment">
+        <plugin name="cooling_off_payment_refund"
+                type="Comave\OrderCoolingOff\Plugin\Sales\PaymentRefundPlugin"
+                sortOrder="5"/>
+    </type>
+
+    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
+        <plugin name="add_cooling_off_grid_fields"
+                type="Comave\OrderCoolingOff\Plugin\Ui\DataProvider\OrderGridDataProvider"/>
+    </type>
+
+    <type name="Webkul\Marketplace\Model\Orders">
+        <plugin name="hide_cooling_off_orders"
+                type="Comave\OrderCoolingOff\Plugin\Marketplace\HideCoolingOffOrders"/>
+    </type>
+
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/events.xml b/app/code/Comave/OrderCoolingOff/etc/events.xml
new file mode 100644
index 000000000..df44b6b5d
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/events.xml
@@ -0,0 +1,25 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
+    
+    <event name="sales_order_place_after">
+        <observer name="set_cooling_off_status"
+                  instance="Comave\OrderCoolingOff\Observer\SetCoolingOffStatus"/>
+    </event>
+
+    <event name="order_cancel_after">
+        <observer name="cooling_off_cancel_after"
+                  instance="Comave\OrderCoolingOff\Observer\OrderCancelAfterObserver"/>
+    </event>
+
+    <event name="sales_order_cancel_after">
+        <observer name="cooling_off_sales_cancel_after"
+                  instance="Comave\OrderCoolingOff\Observer\OrderCancelAfterObserver"/>
+    </event>
+
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/extension_attributes.xml b/app/code/Comave/OrderCoolingOff/etc/extension_attributes.xml
new file mode 100644
index 000000000..602218a41
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/extension_attributes.xml
@@ -0,0 +1,12 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
+    <extension_attributes for="Magento\Sales\Api\Data\OrderInterface">
+        <attribute code="cooling_off_until" type="string"/>
+    </extension_attributes>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/module.xml b/app/code/Comave/OrderCoolingOff/etc/module.xml
new file mode 100644
index 000000000..5b411e4e7
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/module.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
+    <module name="Comave_OrderCoolingOff" setup_version="1.0.1">
+        <sequence>
+            <module name="Magento_Sales"/>
+            <module name="Magento_Customer"/>
+            <module name="Webkul_Marketplace"/>
+            <module name="Comave_Sales"/>
+        </sequence>
+    </module>
+</config>
diff --git a/app/code/Comave/OrderCoolingOff/etc/schema.graphqls b/app/code/Comave/OrderCoolingOff/etc/schema.graphqls
new file mode 100644
index 000000000..06aa29656
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/etc/schema.graphqls
@@ -0,0 +1,3 @@
+type CustomerOrder {
+    cooling_off_until: String @doc(description: "Cooling-off period end time (ISO 8601 format)") @resolver(class: "Comave\\OrderCoolingOff\\Model\\Resolver\\CoolingOffUntil")
+}
diff --git a/app/code/Comave/OrderCoolingOff/registration.php b/app/code/Comave/OrderCoolingOff/registration.php
new file mode 100644
index 000000000..4af908577
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/registration.php
@@ -0,0 +1,11 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+\Magento\Framework\Component\ComponentRegistrar::register(
+    \Magento\Framework\Component\ComponentRegistrar::MODULE,
+    'Comave_OrderCoolingOff',
+    __DIR__
+);
diff --git a/app/code/Comave/OrderCoolingOff/view/adminhtml/layout/sales_order_view.xml b/app/code/Comave/OrderCoolingOff/view/adminhtml/layout/sales_order_view.xml
new file mode 100644
index 000000000..1005f43c0
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/view/adminhtml/layout/sales_order_view.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <body>
+        <referenceBlock name="order_info">
+            <block class="Comave\OrderCoolingOff\Block\Adminhtml\Order\View\CoolingOffStatus"
+                   name="order_cooling_off_status"
+                   template="Comave_OrderCoolingOff::order/view/cooling_off_status.phtml"
+                   after="order_status"/>
+        </referenceBlock>
+    </body>
+</page>
diff --git a/app/code/Comave/OrderCoolingOff/view/adminhtml/templates/order/view/cooling_off_status.phtml b/app/code/Comave/OrderCoolingOff/view/adminhtml/templates/order/view/cooling_off_status.phtml
new file mode 100644
index 000000000..fcff8adce
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/view/adminhtml/templates/order/view/cooling_off_status.phtml
@@ -0,0 +1,12 @@
+<?php
+/**
+ * @var \Comave\OrderCoolingOff\Block\Adminhtml\Order\View\CoolingOffStatus $block
+ */
+?>
+
+<?php if ($block->isCoolingOffStatus() && $block->getCoolingOffExpires()): ?>
+<tr>
+    <th><?= $block->escapeHtml(__('Cooling Off Expires')) ?></th>
+    <td><?= $block->escapeHtml($block->formatExpirationDate($block->getCoolingOffExpires())) ?></td>
+</tr>
+<?php endif; ?>
diff --git a/app/code/Comave/OrderCoolingOff/view/adminhtml/ui_component/sales_order_grid.xml b/app/code/Comave/OrderCoolingOff/view/adminhtml/ui_component/sales_order_grid.xml
new file mode 100644
index 000000000..f06a17859
--- /dev/null
+++ b/app/code/Comave/OrderCoolingOff/view/adminhtml/ui_component/sales_order_grid.xml
@@ -0,0 +1,14 @@
+<?xml version="1.0"?>
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <columns name="sales_order_columns">
+        <column name="cooling_off_expires" class="Comave\OrderCoolingOff\Ui\Component\Listing\Column\CoolingOffExpires">
+            <settings>
+                <label translate="true">Cooling Off Expiry</label>
+                <sortable>true</sortable>
+                <bodyTmpl>ui/grid/cells/text</bodyTmpl>
+                <dataType>date</dataType>
+            </settings>
+        </column>
+    </columns>
+</listing>
diff --git a/app/code/Comave/Sales/Controller/Adminhtml/Order/Cancel.php b/app/code/Comave/Sales/Controller/Adminhtml/Order/Cancel.php
new file mode 100644
index 000000000..7e82a8d0f
--- /dev/null
+++ b/app/code/Comave/Sales/Controller/Adminhtml/Order/Cancel.php
@@ -0,0 +1,109 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Controller\Adminhtml\Order;
+
+use Magento\Backend\App\Action;
+use Magento\Backend\App\Action\Context;
+use Magento\Framework\App\Action\HttpPostActionInterface;
+use Magento\Framework\Controller\Result\JsonFactory;
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Model\Order;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Admin Order Cancellation Controller
+ */
+class Cancel extends Action implements HttpPostActionInterface
+{
+    public const string ADMIN_RESOURCE = 'Magento_Sales::cancel';
+
+    /**
+     * @param Context $context
+     * @param JsonFactory $resultJsonFactory
+     * @param OrderRepositoryInterface $orderRepository
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        Context $context,
+        private readonly JsonFactory $resultJsonFactory,
+        private readonly OrderRepositoryInterface $orderRepository,
+        private readonly LoggerInterface $logger
+    ) {
+        parent::__construct($context);
+    }
+
+    /**
+     * Cancel order with reason
+     *
+     * @return \Magento\Framework\Controller\Result\Json
+     */
+    public function execute()
+    {
+        $resultJson = $this->resultJsonFactory->create();
+
+        try {
+            $orderId = (int) $this->getRequest()->getParam('order_id');
+            $reason = $this->getRequest()->getParam('reason');
+            $comment = $this->getRequest()->getParam('comment', '');
+
+            if (!$orderId) {
+                throw new LocalizedException(__('Order ID is required.'));
+            }
+
+            if (!in_array($reason, ['canceled_by_seller', 'canceled_by_customer'])) {
+                throw new LocalizedException(__('Invalid cancellation reason.'));
+            }
+
+            $order = $this->orderRepository->get($orderId);
+            if (!$order->canCancel()) {
+                throw new LocalizedException(__('This order cannot be cancelled.'));
+            }
+
+            $order->setData('original_cancellation_reason', $reason);
+
+            $order->cancel();
+            $order->setStatus($reason);
+
+            if (!$order->getData('cooling_off_until')) {
+                $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
+
+                if (!empty($comment)) {
+                    $reasonLabel .= ': ' . $comment;
+                }
+
+                $order->addCommentToStatusHistory(
+                    $reasonLabel,
+                    false,
+                    true
+                );
+            }
+
+            $order->save();
+
+            $resultJson->setData([
+                'success' => true,
+                'message' => __('Order has been cancelled successfully.')
+            ]);
+
+        } catch (LocalizedException $e) {
+            $this->logger->error('Order cancellation error: ' . $e->getMessage());
+            $resultJson->setData([
+                'success' => false,
+                'message' => $e->getMessage()
+            ]);
+        } catch (\Exception $e) {
+            $this->logger->error('Order cancellation error: ' . $e->getMessage());
+            $resultJson->setData([
+                'success' => false,
+                'message' => __('An error occurred while cancelling the order.')
+            ]);
+        }
+
+        return $resultJson;
+    }
+}
diff --git a/app/code/Comave/Sales/Observer/EmailTemplateVarsObserver.php b/app/code/Comave/Sales/Observer/EmailTemplateVarsObserver.php
index 72b7cde9c..3cc757f7e 100644
--- a/app/code/Comave/Sales/Observer/EmailTemplateVarsObserver.php
+++ b/app/code/Comave/Sales/Observer/EmailTemplateVarsObserver.php
@@ -40,7 +40,9 @@ class EmailTemplateVarsObserver implements ObserverInterface
             $frontendBaseUrl = $this->deploymentConfig->get(self::FRONTEND_BASE_URL_CONFIG_KEY);
 
             if ($frontendBaseUrl) {
-                $transport->setData(self::FRONTEND_BASE_URL_CONFIG_KEY, rtrim($frontendBaseUrl, '/'));
+                if (is_object($transport) && method_exists($transport, 'setData')) {
+                    $transport->setData(self::FRONTEND_BASE_URL_CONFIG_KEY, rtrim($frontendBaseUrl, '/'));
+                }
             }
         }
     }
diff --git a/app/code/Comave/Sales/Plugin/Adminhtml/Order/View.php b/app/code/Comave/Sales/Plugin/Adminhtml/Order/View.php
new file mode 100644
index 000000000..bc5dbe2f3
--- /dev/null
+++ b/app/code/Comave/Sales/Plugin/Adminhtml/Order/View.php
@@ -0,0 +1,79 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Plugin\Adminhtml\Order;
+
+use Magento\Sales\Block\Adminhtml\Order\View as OrderView;
+use Magento\Framework\AuthorizationInterface;
+use Magento\Framework\UrlInterface;
+
+/**
+ * Plugin to add cancel button to admin order view
+ */
+class View
+{
+    /**
+     * @param AuthorizationInterface $authorization
+     * @param UrlInterface $urlBuilder
+     */
+    public function __construct(
+        private readonly AuthorizationInterface $authorization,
+        private readonly UrlInterface $urlBuilder
+    ) {}
+
+    /**
+     * Replace default cancel button with our custom cancel functionality
+     *
+     * @param OrderView $subject
+     * @return void
+     */
+    public function beforeSetLayout(OrderView $subject): void
+    {
+        $order = $subject->getOrder();
+
+        if (!$this->authorization->isAllowed('Magento_Sales::cancel')) {
+            return;
+        }
+
+        if ($this->canCancelOrder($order)) {
+            $subject->removeButton('order_cancel');
+            $cancelUrl = $this->urlBuilder->getUrl(
+                'comave_sales/order/cancel',
+                ['order_id' => $order->getId()]
+            );
+
+            $subject->addButton(
+                'order_cancel',
+                [
+                    'label' => __('Cancel'),
+                    'class' => 'cancel-order-admin',
+                    'onclick' => "showCancelOrderModal('{$cancelUrl}')",
+                    'sort_order' => 10
+                ]
+            );
+        }
+    }
+
+    /**
+     * Check if order can be cancelled
+     *
+     * @param \Magento\Sales\Model\Order $order
+     * @return bool
+     */
+    private function canCancelOrder($order): bool
+    {
+        if (!$order->canCancel()) {
+            return false;
+        }
+
+        $shippedStatuses = ['shipped', 'delivered', 'pre_transit'];
+        if (in_array($order->getStatus(), $shippedStatuses)) {
+            return false;
+        }
+
+        return true;
+    }
+}
diff --git a/app/code/Comave/Sales/Service/Order/CancellationService.php b/app/code/Comave/Sales/Service/Order/CancellationService.php
new file mode 100644
index 000000000..d90b6f7f2
--- /dev/null
+++ b/app/code/Comave/Sales/Service/Order/CancellationService.php
@@ -0,0 +1,179 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Service\Order;
+
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Sales\Api\OrderRepositoryInterface;
+use Magento\Sales\Model\Order;
+use Magento\Sales\Model\Order\Status\HistoryFactory;
+use Magento\Sales\Api\OrderManagementInterface;
+use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
+use Comave\Sales\Service\Order\RefundService;
+use Comave\Sales\Service\Order\NotificationService;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Order Cancellation Service
+ */
+class CancellationService
+{
+    /**
+     * @param OrderRepositoryInterface $orderRepository
+     * @param HistoryFactory $historyFactory
+     * @param OrderManagementInterface $orderManagement
+     * @param EventManagerInterface $eventManager
+     * @param RefundService $refundService
+     * @param NotificationService $notificationService
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly OrderRepositoryInterface $orderRepository,
+        private readonly HistoryFactory $historyFactory,
+        private readonly OrderManagementInterface $orderManagement,
+        private readonly EventManagerInterface $eventManager,
+        private readonly RefundService $refundService,
+        private readonly NotificationService $notificationService,
+        private readonly LoggerInterface $logger
+    ) {}
+
+    /**
+     * Cancel order with specific reason
+     *
+     * @param Order $order
+     * @param string $reason
+     * @param string $comment
+     * @return void
+     * @throws LocalizedException
+     */
+    public function cancelOrderWithReason(Order $order, string $reason, string $comment = ''): void
+    {
+
+
+        try {
+            if (!$order->canCancel()) {
+                $this->logger->warning(
+                    'Order cancellation failed - order cannot be cancelled',
+                    [
+                        'order_id' => $order->getEntityId(),
+                        'current_status' => $order->getStatus(),
+                        'current_state' => $order->getState()
+                    ]
+                );
+                throw new LocalizedException(__('Order cannot be cancelled.'));
+            }
+
+            $this->orderManagement->cancel($order->getEntityId());
+
+            $order->setStatus($reason);
+            
+            $this->addCancellationComment($order, $reason, $comment);
+
+            $this->orderRepository->save($order);
+
+            $this->processRefundIfNeeded($order);
+
+            $this->notificationService->sendCancellationNotification($order, $reason, $comment);
+
+            $this->eventManager->dispatch(
+                'comave_order_cancelled_with_reason',
+                [
+                    'order' => $order,
+                    'reason' => $reason,
+                    'comment' => $comment
+                ]
+            );
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to cancel order',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+            throw new LocalizedException(__('Failed to cancel order: %1', $e->getMessage()));
+        }
+    }
+
+    /**
+     * Add cancellation comment to order history
+     *
+     * @param Order $order
+     * @param string $reason
+     * @param string $comment
+     * @return void
+     */
+    private function addCancellationComment(Order $order, string $reason, string $comment): void
+    {
+        $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
+        
+        $historyComment = $reasonLabel;
+        if (!empty($comment)) {
+            $historyComment .= ': ' . $comment;
+        }
+
+        $history = $this->historyFactory->create();
+        $history->setParentId($order->getEntityId())
+            ->setStatus($reason)
+            ->setComment($historyComment)
+            ->setIsCustomerNotified(true)
+            ->setIsVisibleOnFront(true);
+
+        $order->addStatusHistory($history);
+    }
+
+    /**
+     * Process refund if payment was captured
+     *
+     * @param Order $order
+     * @return void
+     */
+    public function processRefundIfNeeded(Order $order): void
+    {
+        try {
+            if ($order->hasInvoices() && $order->getPayment()->getAmountPaid() > 0) {
+                $originalState = $order->getState();
+                $originalStatus = $order->getStatus();
+                $needsStateRestore = false;
+
+                if (!$order->canCreditmemo()) {
+                    $this->logger->info(
+                        'Order cannot be refunded in current state - temporarily changing to processing',
+                        [
+                            'order_id' => $order->getEntityId(),
+                            'original_state' => $originalState,
+                            'original_status' => $originalStatus
+                        ]
+                    );
+
+                    $order->setState(Order::STATE_PROCESSING);
+                    $order->setStatus('processing');
+                    $needsStateRestore = true;
+                }
+
+                if ($order->canCreditmemo()) {
+                    $this->refundService->processAutomaticRefund($order);
+                }
+
+                if ($needsStateRestore) {
+                    $order->setState($originalState);
+                    $order->setStatus($originalStatus);
+                }
+
+            }
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to process automatic refund for cancelled order',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage(),
+                    'trace' => $e->getTraceAsString()
+                ]
+            );
+        }
+    }
+}
diff --git a/app/code/Comave/Sales/Service/Order/NotificationService.php b/app/code/Comave/Sales/Service/Order/NotificationService.php
new file mode 100644
index 000000000..3443a426d
--- /dev/null
+++ b/app/code/Comave/Sales/Service/Order/NotificationService.php
@@ -0,0 +1,155 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Service\Order;
+
+use Magento\Framework\Mail\Template\TransportBuilder;
+use Magento\Framework\Translate\Inline\StateInterface;
+use Magento\Store\Model\StoreManagerInterface;
+use Magento\Framework\App\Config\ScopeConfigInterface;
+use Magento\Sales\Model\Order;
+use Magento\Store\Model\ScopeInterface;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Order Cancellation Notification Service
+ */
+class NotificationService
+{
+    private const string XML_PATH_EMAIL_TEMPLATE = 'sales_email/order_comment/template';
+    private const string XML_PATH_EMAIL_IDENTITY = 'sales_email/order_comment/identity';
+    private const string XML_PATH_EMAIL_ENABLED = 'sales_email/order_comment/enabled';
+
+    /**
+     * @param TransportBuilder $transportBuilder
+     * @param StateInterface $inlineTranslation
+     * @param StoreManagerInterface $storeManager
+     * @param ScopeConfigInterface $scopeConfig
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly TransportBuilder $transportBuilder,
+        private readonly StateInterface $inlineTranslation,
+        private readonly StoreManagerInterface $storeManager,
+        private readonly ScopeConfigInterface $scopeConfig,
+        private readonly LoggerInterface $logger
+    ) {}
+
+    /**
+     * Send cancellation notification to customer
+     *
+     * @param Order $order
+     * @param string $reason
+     * @param string $comment
+     * @return void
+     */
+    public function sendCancellationNotification(Order $order, string $reason, string $comment = ''): void
+    {
+        try {
+            if (!$this->isEmailEnabled($order->getStoreId())) {
+                return;
+            }
+
+            $customerEmail = $order->getCustomerEmail();
+            if (!$customerEmail) {
+                $this->logger->warning('No customer email found for order', ['order_id' => $order->getEntityId()]);
+                return;
+            }
+
+            $this->inlineTranslation->suspend();
+
+            $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
+            
+            $templateVars = [
+                'order' => $order,
+                'order_id' => $order->getIncrementId(),
+                'customer_name' => $order->getCustomerName(),
+                'reason' => $reasonLabel,
+                'comment' => $comment,
+                'store' => $order->getStore()
+            ];
+
+            $transport = $this->transportBuilder
+                ->setTemplateIdentifier($this->getEmailTemplate($order->getStoreId()))
+                ->setTemplateOptions([
+                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
+                    'store' => $order->getStoreId()
+                ])
+                ->setTemplateVars($templateVars)
+                ->setFromByScope($this->getEmailIdentity($order->getStoreId()))
+                ->addTo($customerEmail, $order->getCustomerName())
+                ->getTransport();
+
+            $transport->sendMessage();
+
+            $this->inlineTranslation->resume();
+
+            $this->logger->info(
+                'Cancellation notification sent successfully',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'customer_email' => $customerEmail,
+                    'reason' => $reason
+                ]
+            );
+
+        } catch (\Exception $e) {
+            $this->inlineTranslation->resume();
+            $this->logger->error(
+                'Failed to send cancellation notification',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+        }
+    }
+
+    /**
+     * Check if email notifications are enabled
+     *
+     * @param int $storeId
+     * @return bool
+     */
+    private function isEmailEnabled(int $storeId): bool
+    {
+        return $this->scopeConfig->isSetFlag(
+            self::XML_PATH_EMAIL_ENABLED,
+            ScopeInterface::SCOPE_STORE,
+            $storeId
+        );
+    }
+
+    /**
+     * Get email template
+     *
+     * @param int $storeId
+     * @return string
+     */
+    private function getEmailTemplate(int $storeId): string
+    {
+        return $this->scopeConfig->getValue(
+            self::XML_PATH_EMAIL_TEMPLATE,
+            ScopeInterface::SCOPE_STORE,
+            $storeId
+        );
+    }
+
+    /**
+     * Get email identity
+     *
+     * @param int $storeId
+     * @return string
+     */
+    private function getEmailIdentity(int $storeId): string
+    {
+        return $this->scopeConfig->getValue(
+            self::XML_PATH_EMAIL_IDENTITY,
+            ScopeInterface::SCOPE_STORE,
+            $storeId
+        );
+    }
+}
diff --git a/app/code/Comave/Sales/Service/Order/RefundService.php b/app/code/Comave/Sales/Service/Order/RefundService.php
new file mode 100644
index 000000000..0ca8b346b
--- /dev/null
+++ b/app/code/Comave/Sales/Service/Order/RefundService.php
@@ -0,0 +1,146 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Service\Order;
+
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Sales\Model\Order;
+use Magento\Sales\Model\Order\CreditmemoFactory;
+use Magento\Sales\Api\CreditmemoManagementInterface;
+use Magento\Sales\Model\Order\Invoice;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Refund Service for Order Cancellations
+ */
+class RefundService
+{
+    /**
+     * @param CreditmemoFactory $creditmemoFactory
+     * @param CreditmemoManagementInterface $creditmemoManagement
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly CreditmemoFactory $creditmemoFactory,
+        private readonly CreditmemoManagementInterface $creditmemoManagement,
+        private readonly LoggerInterface $logger
+    ) {}
+
+    /**
+     * Process automatic refund for cancelled order
+     *
+     * @param Order $order
+     * @return void
+     * @throws LocalizedException
+     */
+    public function processAutomaticRefund(Order $order): void
+    {
+
+
+        if (!$order->canCreditmemo()) {
+            return;
+        }
+
+        try {
+            $invoice = $this->getLastInvoice($order);
+            if (!$invoice) {
+                $this->logger->warning(
+                    'No invoice found for refund - cannot process automatic refund',
+                    [
+                        'order_id' => $order->getEntityId(),
+                        'increment_id' => $order->getIncrementId(),
+                        'invoice_collection_size' => $order->getInvoiceCollection()->getSize()
+                    ]
+                );
+                return;
+            }
+
+            $creditmemo = $this->creditmemoFactory->createByInvoice($invoice);
+            if (!$creditmemo) {
+                throw new LocalizedException(__('Cannot create credit memo for this order.'));
+            }
+
+            $creditmemo->addComment(
+                __('Automatic refund due to order cancellation'),
+                false,
+                true
+            );
+
+            $isOnline = $this->canRefundOnline($order);
+
+            if ($isOnline) {
+                $creditmemo->setDoTransaction(true);
+            }
+
+            $this->creditmemoManagement->refund($creditmemo, !$isOnline);
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to process automatic refund',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+            throw new LocalizedException(__('Failed to process refund: %1', $e->getMessage()));
+        }
+    }
+
+    /**
+     * Get the last invoice for the order
+     *
+     * @param Order $order
+     * @return Invoice|null
+     */
+    private function getLastInvoice(Order $order): ?Invoice
+    {
+        $invoices = $order->getInvoiceCollection();
+        
+        if ($invoices->getSize() === 0) {
+            return null;
+        }
+
+        $invoices->setOrder('created_at', 'DESC');
+        return $invoices->getFirstItem();
+    }
+
+    /**
+     * Check if order can be refunded online
+     *
+     * @param Order $order
+     * @return bool
+     */
+    private function canRefundOnline(Order $order): bool
+    {
+        $payment = $order->getPayment();
+        if (!$payment) {
+            $this->logger->warning('No payment found for order', ['order_id' => $order->getEntityId()]);
+            return false;
+        }
+
+        $paymentMethod = $payment->getMethodInstance();
+        $canRefund = $paymentMethod->canRefund();
+        $amountPaid = $payment->getAmountPaid();
+        $baseAmountPaidOnline = $payment->getBaseAmountPaidOnline();
+
+        if ($payment->getMethod() === 'stripe_payments' && $payment->getLastTransId()) {
+            $lastTransId = $payment->getLastTransId();
+            if (strpos($lastTransId, 'pi_') === 0 || strpos($lastTransId, 'ch_') === 0) {
+                $this->logger->info(
+                    'Stripe payment detected with transaction ID - enabling online refund',
+                    [
+                        'order_id' => $order->getEntityId(),
+                        'transaction_id' => $lastTransId,
+                        'forcing_online_refund' => true
+                    ]
+                );
+                return true;
+            }
+        }
+
+        return $canRefund && $amountPaid > 0 && $baseAmountPaidOnline > 0;
+    }
+}
diff --git a/app/code/Comave/Sales/Setup/Patch/Data/CreateCancellationOrderStatuses.php b/app/code/Comave/Sales/Setup/Patch/Data/CreateCancellationOrderStatuses.php
new file mode 100644
index 000000000..87283c5e0
--- /dev/null
+++ b/app/code/Comave/Sales/Setup/Patch/Data/CreateCancellationOrderStatuses.php
@@ -0,0 +1,131 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Setup\Patch\Data;
+
+use Magento\Framework\Setup\Patch\DataPatchInterface;
+use Magento\Framework\Setup\ModuleDataSetupInterface;
+use Magento\Sales\Model\Order\StatusFactory;
+use Magento\Sales\Model\ResourceModel\Order\Status as StatusResource;
+use Magento\Sales\Model\Order\Status;
+use Magento\Sales\Model\Order;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Create custom cancellation order statuses
+ */
+class CreateCancellationOrderStatuses implements DataPatchInterface
+{
+    /**
+     * @param ModuleDataSetupInterface $moduleDataSetup
+     * @param StatusFactory $statusFactory
+     * @param StatusResource $statusResource
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly ModuleDataSetupInterface $moduleDataSetup,
+        private readonly StatusFactory $statusFactory,
+        private readonly StatusResource $statusResource,
+        private readonly LoggerInterface $logger
+    ) {}
+
+    /**
+     * Apply data patch
+     *
+     * @return void
+     */
+    public function apply(): void
+    {
+        $this->moduleDataSetup->getConnection()->startSetup();
+
+        $statusesToCreate = [
+            [
+                'status' => 'canceled_by_seller',
+                'label' => 'Canceled by Seller',
+                'state' => Order::STATE_CANCELED,
+                'is_default' => false,
+                'visible_on_front' => true
+            ],
+            [
+                'status' => 'canceled_by_customer',
+                'label' => 'Canceled by Customer',
+                'state' => Order::STATE_CANCELED,
+                'is_default' => false,
+                'visible_on_front' => true
+            ]
+        ];
+
+        foreach ($statusesToCreate as $statusData) {
+            try {
+                $this->createOrderStatus($statusData);
+            } catch (\Exception $e) {
+                $this->logger->error(
+                    'Failed to create order status: ' . $statusData['status'],
+                    ['error' => $e->getMessage()]
+                );
+            }
+        }
+
+        $this->moduleDataSetup->getConnection()->endSetup();
+    }
+
+    /**
+     * Create order status if it doesn't exist
+     *
+     * @param array $statusData
+     * @return void
+     * @throws \Exception
+     */
+    private function createOrderStatus(array $statusData): void
+    {
+        $status = $this->statusFactory->create();
+        $status->load($statusData['status']);
+
+        if ($status->getStatus()) {
+            if ($status->getLabel() !== $statusData['label']) {
+                $status->setLabel($statusData['label']);
+                $this->statusResource->save($status);
+                $this->logger->info('Updated order status label: ' . $statusData['status']);
+            }
+            return;
+        }
+
+        $status->setData([
+            'status' => $statusData['status'],
+            'label' => $statusData['label']
+        ]);
+
+        $this->statusResource->save($status);
+
+        $status->assignState(
+            $statusData['state'],
+            $statusData['is_default'],
+            $statusData['visible_on_front']
+        );
+
+        $this->logger->info('Created order status: ' . $statusData['status']);
+    }
+
+    /**
+     * Get dependencies
+     *
+     * @return array
+     */
+    public static function getDependencies(): array
+    {
+        return [];
+    }
+
+    /**
+     * Get aliases
+     *
+     * @return array
+     */
+    public function getAliases(): array
+    {
+        return [];
+    }
+}
diff --git a/app/code/Comave/Sales/etc/adminhtml/routes.xml b/app/code/Comave/Sales/etc/adminhtml/routes.xml
new file mode 100644
index 000000000..cbfbcf549
--- /dev/null
+++ b/app/code/Comave/Sales/etc/adminhtml/routes.xml
@@ -0,0 +1,14 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:App/etc/routes.xsd">
+    <router id="admin">
+        <route id="comave_sales" frontName="comave_sales">
+            <module name="Comave_Sales" />
+        </route>
+    </router>
+</config>
diff --git a/app/code/Comave/Sales/etc/di.xml b/app/code/Comave/Sales/etc/di.xml
index 7245d8da0..c4ac587fc 100644
--- a/app/code/Comave/Sales/etc/di.xml
+++ b/app/code/Comave/Sales/etc/di.xml
@@ -152,4 +152,8 @@
     <type name="Magento\Ui\Model\Export\MetadataProvider">
         <plugin name="comave_sales_format_sku_ean_export" type="Comave\Sales\Plugin\FormatSkuEanExport" sortOrder="10" />
     </type>
+    <type name="Magento\Sales\Block\Adminhtml\Order\View">
+        <plugin name="addCancelButton" type="Comave\Sales\Plugin\Adminhtml\Order\View"/>
+    </type>
+
 </config>
diff --git a/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml b/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
index 837d49e11..8679c7ef9 100644
--- a/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
+++ b/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
@@ -20,5 +20,10 @@
         </referenceContainer>
         <referenceBlock name="gift_options" remove="true" />
         <referenceBlock name="gift_options_link" remove="true" />
+        <referenceContainer name="content">
+            <block class="Magento\Framework\View\Element\Template"
+                   name="order_cancel_modal"
+                   template="Comave_Sales::order/cancel_modal.phtml" />
+        </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Comave/Sales/view/adminhtml/requirejs-config.js b/app/code/Comave/Sales/view/adminhtml/requirejs-config.js
new file mode 100644
index 000000000..4842270b1
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/requirejs-config.js
@@ -0,0 +1,10 @@
+/**
+ * Copyright © Commercial Avenue
+ */
+var config = {
+    map: {
+        '*': {
+            orderCancelModal: 'Comave_Sales/js/order-cancel-modal'
+        }
+    }
+};
diff --git a/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml b/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml
new file mode 100644
index 000000000..fc7ea928d
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml
@@ -0,0 +1,114 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ *
+ * @var \Magento\Framework\View\Element\Template $block
+ */
+?>
+
+<!-- Cancel Order Modal -->
+<div id="cancel-order-modal" style="display: none;">
+    <div class="cancel-order-content">
+        <form id="cancel-order-form">
+            <div class="cancel-reason-options">
+                <div class="reason-option">
+                    <input type="radio"
+                           id="reason_seller"
+                           name="reason"
+                           value="canceled_by_seller"
+                           required />
+                    <label for="reason_seller">
+                        <?= $block->escapeHtml(__('Canceled by Seller')) ?>
+                    </label>
+                </div>
+                <div class="reason-option">
+                    <input type="radio"
+                           id="reason_customer"
+                           name="reason"
+                           value="canceled_by_customer"
+                           required />
+                    <label for="reason_customer">
+                        <?= $block->escapeHtml(__('Canceled by Customer')) ?>
+                    </label>
+                </div>
+            </div>
+
+            <div class="cancel-comment">
+                <textarea id="cancel_comment"
+                          name="comment"
+                          placeholder="<?= $block->escapeHtmlAttr(__('Comment (optional)')) ?>"></textarea>
+            </div>
+
+            <div class="cancel-actions">
+                <button type="button" class="action-secondary cancel-modal-btn">
+                    <?= $block->escapeHtml(__('Close')) ?>
+                </button>
+                <button type="button" class="action-primary submit-cancel-btn">
+                    <?= $block->escapeHtml(__('Cancel Order')) ?>
+                </button>
+            </div>
+        </form>
+    </div>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#cancel-order-modal": {
+        "Comave_Sales/js/order-cancel-modal": {}
+    }
+}
+</script>
+
+<style type="text/css">
+.cancel-order-modal .modal-inner-wrap {
+    max-width: 400px;
+}
+
+.cancel-order-content {
+    padding: 20px;
+}
+
+.cancel-reason-options {
+    margin-bottom: 20px;
+}
+
+.reason-option {
+    margin-bottom: 12px;
+    display: flex;
+    align-items: center;
+}
+
+.reason-option input[type="radio"] {
+    margin-right: 8px;
+    margin-top: 0;
+}
+
+.reason-option label {
+    cursor: pointer;
+    font-weight: normal;
+    margin: 0;
+}
+
+.cancel-comment {
+    margin-bottom: 20px;
+}
+
+.cancel-comment textarea {
+    width: 100%;
+    min-height: 60px;
+    padding: 8px;
+    border: 1px solid #ccc;
+    border-radius: 3px;
+    resize: vertical;
+    font-family: inherit;
+}
+
+.cancel-actions {
+    text-align: right;
+}
+
+.cancel-actions button {
+    margin-left: 10px;
+    padding: 8px 16px;
+}
+</style>
diff --git a/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js b/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js
new file mode 100644
index 000000000..950534ef7
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js
@@ -0,0 +1,162 @@
+/**
+ * Copyright © Commercial Avenue
+ */
+define([
+    'jquery',
+    'Magento_Ui/js/modal/modal',
+    'Magento_Ui/js/modal/alert',
+    'mage/translate',
+    'mage/url'
+], function ($, modal, alert, $t, urlBuilder) {
+    'use strict';
+
+    $.widget('comave.orderCancelModal', {
+        options: {
+            modalSelector: '#cancel-order-modal',
+            formSelector: '#cancel-order-form',
+            submitButtonSelector: '.submit-cancel-btn',
+            cancelButtonSelector: '.cancel-modal-btn',
+            cancelUrl: null,
+            modalOptions: {
+                type: 'popup',
+                responsive: true,
+                innerScroll: true,
+                title: null,
+                modalClass: 'cancel-order-modal',
+                buttons: []
+            }
+        },
+
+        _create: function () {
+            this._initModal();
+            this._bindEvents();
+        },
+
+        _initModal: function () {
+            var modalOptions = $.extend({}, this.options.modalOptions, {
+                title: $t('Cancel Order')
+            });
+
+            this.modal = modal(modalOptions, $(this.options.modalSelector));
+        },
+
+        _bindEvents: function () {
+            var self = this;
+
+            $(this.options.submitButtonSelector).on('click', function (e) {
+                e.preventDefault();
+                self._submitCancelOrder();
+            });
+
+            $(this.options.cancelButtonSelector).on('click', function (e) {
+                e.preventDefault();
+                self.closeModal();
+            });
+
+            $(this.options.formSelector).on('submit', function (e) {
+                e.preventDefault();
+                self._submitCancelOrder();
+            });
+        },
+
+        openModal: function (cancelUrl) {
+            this.options.cancelUrl = cancelUrl;
+            this._resetForm();
+            this.modal.openModal();
+        },
+
+        closeModal: function () {
+            this.modal.closeModal();
+        },
+
+        _resetForm: function () {
+            var form = $(this.options.formSelector);
+            form.find('input[type="radio"]').prop('checked', false);
+            form.find('textarea').val('');
+        },
+
+        _submitCancelOrder: function () {
+            var self = this;
+            var form = $(this.options.formSelector);
+            var reason = form.find('input[name="reason"]:checked').val();
+            var comment = form.find('textarea[name="comment"]').val();
+
+            if (!reason) {
+                alert({
+                    content: $t('Please select a cancellation reason.')
+                });
+                return;
+            }
+
+            if (!this.options.cancelUrl) {
+                alert({
+                    content: $t('Cancel URL is not set.')
+                });
+                return;
+            }
+
+            $('body').trigger('processStart');
+
+            $.ajax({
+                url: this.options.cancelUrl,
+                type: 'POST',
+                data: {
+                    reason: reason,
+                    comment: comment,
+                    form_key: window.FORM_KEY
+                },
+                dataType: 'json',
+                success: function (response) {
+                    $('body').trigger('processStop');
+                    
+                    if (response.success) {
+                        alert({
+                            content: response.message || $t('Order has been cancelled successfully.'),
+                            actions: {
+                                always: function () {
+                                    location.reload();
+                                }
+                            }
+                        });
+                    } else {
+                        alert({
+                            content: response.message || $t('An error occurred while cancelling the order.')
+                        });
+                    }
+                },
+                error: function (xhr, status, error) {
+                    $('body').trigger('processStop');
+                    console.error('Cancel order error:', error);
+                    alert({
+                        content: $t('An error occurred while cancelling the order. Please try again.')
+                    });
+                }
+            });
+
+            this.closeModal();
+        },
+
+        destroy: function () {
+            if (this.modal) {
+                this.modal.closeModal();
+            }
+            this._super();
+        }
+    });
+
+    window.showCancelOrderModal = function (url) {
+        var modalWidget = $('#cancel-order-modal').data('comave-orderCancelModal');
+        if (modalWidget) {
+            modalWidget.openModal(url);
+        }
+    };
+
+    window.closeCancelOrderModal = function () {
+        var modalWidget = $('#cancel-order-modal').data('comave-orderCancelModal');
+        if (modalWidget) {
+            modalWidget.closeModal();
+        }
+    };
+
+    return $.comave.orderCancelModal;
+});
diff --git a/app/etc/config.php b/app/etc/config.php
index 2bec5d2f1..abb9b48aa 100644
--- a/app/etc/config.php
+++ b/app/etc/config.php
@@ -912,6 +912,7 @@ return [
         'Webkul_MarketplaceBaseShipping' => 1,
         'Webkul_MarketplacePreorder' => 1,
         'Comave_RmaGraphQl' => 1,
+        'Comave_OrderCoolingOff' => 1,
         'Comave_CustomerLogin' => 1,
         'Comave_ProductOffers' => 1,
         'Comave_TrackingStatus' => 0,
