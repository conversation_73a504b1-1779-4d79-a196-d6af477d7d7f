<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
namespace Webkul\MpApi\Api;

interface SellerManagementInterface
{
    /**
     * Interface for specific seller details.
     *
     * @param int $id
     * @param int $storeId
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSeller($id, $storeId = 0);

    /**
     * Get seller details
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerList(\Magento\Framework\Api\SearchCriteriaInterface $searchCriteria);

    /**
     * Get seller products.
     *
     * @param int $id
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function getSellerProducts($id);

    /**
     * Interface for specific seller details.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerSalesList($id);

    /**
     * Interface for getting seller sales details.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerSalesDetails($id);

    /**
     * Interface for creating seller order invoice.
     *
     * @param int $id
     * @param int $orderId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function createInvoice($id, $orderId);

    /**
     * Interface to view invoice.
     *
     * @param int $id
     * @param int $orderId
     * @param int $invoiceId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function viewInvoice($id, $orderId, $invoiceId);

    /**
     * Interface for cancel order.
     *
     * @param int $id
     * @param int $orderId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function cancelOrder($id, $orderId);

    /**
     * Interface for creating credit memo.
     *
     * @param int $id
     * @param int $invoiceId
     * @param int $orderId
     * @param \Webkul\MpApi\Api\Data\CreditMemoInterface $creditMemo
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function createCreditmemo($id, $invoiceId, $orderId, $creditMemo);

    /**
     * Interface to view credit memo.
     *
     * @param int $id
     * @param int $orderId
     * @param int $creditmemoId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function viewCreditmemo($id, $orderId, $creditmemoId);

    /**
     * Interface for generating shipment.
     *
     * @param int $id
     * @param int $orderId
     * @param string $trackingId
     * @param string $carrier
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function ship($id, $orderId, $trackingId, $carrier);

    /**
     * Interface to view shipment.
     *
     * @param int $id
     * @param int $orderId
     * @param int $shipmentId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function viewShipment($id, $orderId, $shipmentId);

    /**
     * Interface for mail to admin.
     *
     * @param int $id Seller Id
     * @param string $subject
     * @param string $query
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function mailToAdmin($id, $subject, $query);

    /**
     * Interface for mail to seller.
     *
     * @param int $id
     * @param string $subject
     * @param string $query
     * @param int $productId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function mailToSeller($id, $subject, $query, $productId);

    /**
     * Interface for mail to seller by seller id.
     *
     * @param int $id Seller Id
     * @param string $subject
     * @param string $query
     * @param int $sellerId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function mailToSellerBySellerId($id, $subject, $query, $sellerId);

    /**
     * Become partner.
     *
     * @param int $id
     * @param string $shopUrl
     * @param int $isSeller
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function becomePartner($id, $shopUrl, $isSeller);

    /**
     * Get landing page data.
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function getLandingPageData();

    /**
     * Get seller reviews .
     *
     * @param int $id
     *
     * @return string Magento\Framework\Api\SearchResults
     */
    public function getSellerReviews($id);

    /**
     * Get seller reviews .
     *
     * @param int $sellerId
     * @param \Webkul\MpApi\Api\Data\FeedbackInterface $feedback
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function makeSellerReview($sellerId, \Webkul\MpApi\Api\Data\FeedbackInterface $feedback);

    /**
     * Get review by Review Id
     *
     * @param int $reviewId
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getReview($reviewId);

    /**
     * Create seller account
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @param string $password
     * @param int $isSeller
     * @param string $profileurl
     * @param int $registered
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function createAccount(
        \Magento\Customer\Api\Data\CustomerInterface $customer,
        $password,
        $isSeller,
        $profileurl,
        $registered
    );

    /**
     * Create product
     *
     * @param int $id
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function saveProduct($id);

    /**
     * Get landing page data
     *
     * @param int $sellerId
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function uploadImage($sellerId);

    /**
     * Upload multiple images
     *
     * @param int $sellerId
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function uploadMultipleImages($sellerId);

}
