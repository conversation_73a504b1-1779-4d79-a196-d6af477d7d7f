<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
namespace Webkul\MpApi\Api;

interface AdminManagementInterface
{
    /**
     * Get seller details.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return Magento\Framework\Api\SearchResults
     */
    public function getSellerListForAdmin(\Magento\Framework\Api\SearchCriteriaInterface $searchCriteria);

    /**
     * Get seller products.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerProducts($id);

    /**
     * Interface for specific seller details.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults[]
     */
    public function getSellerForAdmin($id);

    /**
     * Interface for specific seller details.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerSalesList($id);

    /**
     * Interface for seller order details.
     *
     * @param int $id
     *
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getSellerSalesDetails($id);

    /**
     * Interface for paying the seller.
     *
     * @param int $sellerId
     * @param string $sellerPayReason
     * @param int $entityId
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function payToSeller($sellerId, $sellerPayReason, $entityId);

    /**
     * Interface for assign product(s) to the seller.
     *
     * @param int $sellerId
     * @param int[] $productIds
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function assignProduct($sellerId, $productIds);

    /**
     * Interface for assign product(s) to the seller.
     *
     * @param int $sellerId
     * @param int[] $productIds
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function unassignProduct($sellerId, $productIds);

    /**
     * Interface for creating seller flag reason.
     *
     * @param string $reason
     * @param int $status
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function createSellerFlagReason($reason, $status);

    /**
     * Interface for creating seller flag reason.
     *
     * @param string $reason
     * @param int $status
     *
     * @return string \Magento\Framework\Controller\Result\Json
     */
    public function createProductFlagReason($reason, $status);

}
