<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="before_seller_status_change">
        <observer name="checkForOnboarding" instance="Comave\SellerStatusOnboarding\Observer\CheckFinishedOnboarding"/>
    </event>
    <event name="after_seller_status_change">
        <observer name="updateKnowYorBusinessStatus"
                  instance="Comave\SellerStatusOnboarding\Observer\UpdateKnowYorBusinessStatus"/>
    </event>
</config>
