<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Observer;

use Comave\Marketplace\Model\Service\KnowYourBusiness;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class SetKnowYorBusinessStatus implements ObserverInterface
{
    public function __construct(private readonly KnowYourBusiness $knowYourBusinessService)
    {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function execute(Observer $observer): void
    {
        $seller = $observer->getData('data_object');
        $sellerId = $seller->getSellerId();
        if (!is_numeric($sellerId)) {
            throw new LocalizedException(__('Unable to determine seller, ID missing'));
        }
        $this->knowYourBusinessService->updateSellerStatusByRole($sellerId);
    }
}
