<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Model\Queue\Consumer;

use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\ImportEntityRepositoryInterface;
use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Comave\MarketplaceCatalogImport\Model\Command\ImportDataPreProcess;
use Comave\MarketplaceCatalogImport\Model\Command\ImportedRowsProvider;
use Comave\MarketplaceCatalogImport\Service\EmailNotifier;
use Comave\SellerApi\Model\MediaGalleryRegistry;
use Magento\Catalog\Api\Data\ProductInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\Seller as SellerModel;
use Webkul\Marketplace\Model\SellerFactory;

class StartImportProcess
{
    public const string TOPIC_NAME = 'comave.product.import';

    /**
     * @param StoreManagerInterface $storeManager
     * @param ProductRepositoryInterface $productRepository
     * @param ProductInterfaceFactory $productFactory
     * @param SellerFactory $sellerFactory
     * @param Seller $sellerResource
     * @param LoggerInterface $logger
     * @param ImportedRowsProvider $importedRowsProvider
     * @param EmailNotifier $emailNotifier
     * @param ImportDataPreProcess $importDataPreProcess
     * @param MediaGalleryRegistry $mediaGalleryRegistry
     * @param ImportEntityRepositoryInterface $importEntityRepository
     * @param ProductDataHandlerInterface[] $productDataHandlers
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly ProductInterfaceFactory $productFactory,
        private readonly SellerFactory $sellerFactory,
        private readonly Seller $sellerResource,
        private readonly LoggerInterface $logger,
        private readonly ImportedRowsProvider $importedRowsProvider,
        private readonly EmailNotifier $emailNotifier,
        private readonly ImportDataPreProcess $importDataPreProcess,
        private readonly MediaGalleryRegistry $mediaGalleryRegistry,
        private readonly ImportEntityRepositoryInterface $importEntityRepository,
        private readonly array $productDataHandlers = []
    ) {
    }

    /**
     * @param int $importEntityId
     * @return void
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute(int $importEntityId): void
    {
        $this->logger->info(
            '[ComaveImportProducts] Beginning product import',
            [
                'importEntity' => $importEntityId
            ]
        );

        $importEntity = $this->importEntityRepository->get($importEntityId);
        $start = microtime(true);
        $importEntity->setStartedAt((string) time());
        $this->importEntityRepository->save($importEntity);
        $rows = $this->importedRowsProvider->getRows($importEntity);
        $importData = $this->importDataPreProcess->preprocess($rows);
        $errorContext = [];
        $rowNr = 1;
        $seller = $this->sellerFactory->create();
        $this->sellerResource->load($seller, $importEntity->getSellerId(), 'seller_id');
        $totalImported = 0;
        $this->storeManager->setCurrentStore(0);

        foreach ($importData as $productRow) {
            $result = $this->processProductData(
                $importEntity,
                $seller,
                $rowNr,
                $productRow,
                $errorContext
            );

            if ($result) {
                $totalImported++;
            }

            $rowNr++;

            if (isset($productRow['children'])) {
                $totalImported += count($productRow['children']);
                $rowNr += count($productRow['children']);
            }
        }

        $importEntity->setErrors($errorContext)
            ->setProcessedAt((string) time());
        $this->importEntityRepository->save($importEntity);
        $this->mediaGalleryRegistry->process();
        $end = microtime(true);
        $this->logger->info(
            '[ComaveImportProducts] Finished export',
            [
                'importEntity' => $importEntityId,
                'elapsedTime' => $end - $start,
            ]
        );
        $this->sendResultEmail(
            $seller,
            (int) $totalImported,
            $errorContext
        );
    }

    /**
     * @param SellerModel $seller
     * @param int $totalImported
     * @param array $errorContext
     * @return void
     */
    private function sendResultEmail(SellerModel $seller, int $totalImported, array $errorContext): void
    {
        try {
            $errorHtml = '';

            if (!empty($errorContext)) {
                $errorHtml = '<ul>';

                foreach ($errorContext as $error) {
                    $errorHtml .= '<li>' . htmlspecialchars($error) . '</li>';
                }

                $errorHtml .= '</ul>';
            }

            $this->emailNotifier->sendEmail(
                $seller,
                'comave_seller_import_products_results',
                [
                    'errors' => $errorHtml,
                    'errorCount' => count($errorContext),
                    'totalImported' => $totalImported
                ]
            );
        } catch (\Exception) {//this is mostly for lower level instances where emails are not handled properly
        }
    }

    /**
     * @param ImportEntityInterface $importEntity
     * @param SellerModel $seller
     * @param int $rowNr
     * @param array $productRow
     * @param array $errorContext
     * @return bool
     */
    private function processProductData(
        ImportEntityInterface $importEntity,
        SellerModel $seller,
        int $rowNr,
        array $productRow,
        array &$errorContext
    ): bool {
        $store = $this->storeManager->getDefaultStoreView();
        $storeId = $store->getId();
        $productWebsite = $store->getWebsiteId();

        if ($seller->getStoreId() > 0) {
            $storeId = $seller->getStoreId();

            try {
                $productWebsite = $this->storeManager->getStore($seller->getStoreId())->getWebsiteId();
            } catch (\Throwable) {//nothing to do, assigning to default store in case the seller store is not properly set
            }
        }

        $product = $this->productFactory->create();

        try {
            $productSku = $productRow['sku'] ?? false;

            if ($productSku === false) {
                throw new LocalizedException(
                    __('Unable to identify the product SKU for row %1', $rowNr)
                );
            }

            $product = $this->productRepository->get($productSku, true, $storeId, true);
        } catch (NoSuchEntityException) {
            $product->setAttributeSetId($product->getDefaultAttributeSetId())
                ->setStatus(Status::STATUS_DISABLED)
                ->setTypeId($productRow['type'] ?? 'simple')
                ->setVisibility(
                    Visibility::VISIBILITY_BOTH
                );
            $product->setWebsiteIds([$productWebsite]);
            $product->setUrlKey($productRow['url_key'] ?? $productSku);
        } catch (\Throwable $t) {
            $errorContext[$rowNr] = $t->getMessage();

            return false;
        }

        $product->addData($productRow);
        $product->setData(
            'assign_seller',
            [
                'seller_id' => $importEntity->getSellerId()
            ]
        );

        try {
            foreach ($this->productDataHandlers as $handler) {
                $handler->handle($product, $productRow);
            }

            //core limitation for stocks -> save is called individually for each product
            $product->save();

            return true;
        } catch (\Throwable $t) {
            $errorContext[$rowNr] = $t->getMessage();

            return false;
        }
    }
}
