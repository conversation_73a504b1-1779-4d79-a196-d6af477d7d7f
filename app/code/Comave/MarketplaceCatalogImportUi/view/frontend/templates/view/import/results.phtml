<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
$styleString = <<<STYLESTRING
    #csv-upload-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #f5f5f5;
        padding: 30px;
        box-sizing: border-box;
    }

    .csv-upload-form {
        background: #fff;
        margin: 0 auto;
        padding: 30px 40px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        min-width: 320px;
        max-width: 600px;
        width: 100%;
        box-sizing: border-box;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin-bottom: 30px;
    }

    .csv-upload-form .field {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
    }

    .csv-upload-form .label span {
        font-weight: 600;
        margin-bottom: 8px;
        font-size: 1.1em;
        color: #333;
    }

    .csv-upload-form .control input[type="file"] {
        padding: 6px;
        font-size: 1em;
        cursor: pointer;
    }

    .csv-upload-form .error-message {
        color: #d32f2f;
        font-size: 0.9em;
        margin-top: 6px;
        display: none;
    }

    .csv-upload-form .actions {
        text-align: center;
    }

    .csv-upload-form .action.primary {
        background-color: #007bdb;
        border: none;
        color: white;
        padding: 12px 25px;
        font-size: 1em;
        font-weight: 600;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .csv-upload-form .action.primary:hover {
        background-color: #005fa3;
    }

    #csv-validation-results {
        margin-top: 20px;
        font-size: 14px;
        color: #444;
        white-space: pre-wrap;
        overflow: scroll;
        max-height: 50rem;
    }

    #results-rows {
        overflow: auto;
        background: white;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        border-radius: 6px;
        padding: 20px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        font-size: 14px;
        color: #222;
    }

    #results-rows > div {
        word-break: break-word;
    }
STYLESTRING;
$scriptString = <<<SCRIPTSTRING
require([
    'jquery'
], function ($) {
    'use strict';

    const form = document.getElementById('csv-upload-form');
    const fileInput = document.getElementById('csv_file');
    const errorDiv = document.getElementById('csv_file_error');
    const resultsDiv = document.getElementById('csv-validation-results');

    function isCSV(file) {
        return file && file.name.toLowerCase().endsWith('.csv');
    }

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        errorDiv.style.display = 'none';
        resultsDiv.textContent = '';

        const file = fileInput.files[0];
        if (!isCSV(file)) {
            errorDiv.style.display = 'block';
            return;
        }

        const formData = new FormData(form);
        resultsDiv.textContent = 'Uploading and validating...';
        resultsDiv.style.color = 'green';

        $('body').trigger('processStart');
        fetch('{$block->getUrl('marketplace/catalog_import/upload')}', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            const rows = document.getElementById('results-rows');
            const processButton = document.getElementById('process-products');
            processButton.style.display = 'none';
            rows.innerHTML = data.rowsHtml;
            rows.style.display = 'block';

            $('body').trigger('processStop');
            if (data.success) {
                resultsDiv.style.color = 'green';
                resultsDiv.textContent = data.message || 'CSV validated successfully.';
                processButton.style.display = 'block';
            } else {
                resultsDiv.style.color = '#d32f2f';
                if(data.errors) {
                    resultsDiv.textContent = data.errors.join('\\n');
                } else if(data.error) {
                    resultsDiv.textContent = data.error;
                } else {
                    resultsDiv.textContent = 'Validation failed with unknown error.';
                }
            }
        })
        .catch(() => {
            $('body').trigger('processStop');
            resultsDiv.style.color = '#d32f2f';
            resultsDiv.textContent = 'An error occurred while uploading.';
        });
    });
});
SCRIPTSTRING;

?>
<?= /** @noEscape */ $secureRenderer->renderTag('style', [], $styleString, false); ?>

<div class="csv-upload-wrapper">
    <form id="csv-upload-form"
          action="<?= $block->getUrl('marketplace/catalog_import/upload') ?>"
          method="post"
          enctype="multipart/form-data"
          class="csv-upload-form">
        <?= $block->getBlockHtml('formkey') ?>

        <div class="field">
            <label for="csv_file" class="label">
                <span><?= __('Upload Product CSV File') ?></span>
            </label>
            <div class="control">
                <input
                    type="file"
                    name="csv_file"
                    id="csv_file"
                    accept=".csv"
                    required
                    aria-required="true"
                    aria-describedby="csv_file_error"
                />
                <div id="csv_file_error" class="error-message">
                    <?= __('Please upload a valid CSV file.') ?>
                </div>
            </div>
        </div>

        <div class="actions">
            <button type="submit" class="action primary">
                <span><?= __('Upload and Validate') ?></span>
            </button>
        </div>

        <div id="csv-validation-results"></div>
    </form>
    <div id="results-rows" style="display: none;">
    </div>
    <div style="display: flex; padding-top: 25px; justify-content: center;">
        <button type="button"
                onclick="window.location = '<?= $escaper->escapeUrl($block->getUrl('marketplace/catalog_import/process')); ?>'"
                class="action primary"
                id="process-products"
                style="display: none;">
            <?= /** @noEscape  */__('Process products'); ?>
        </button>
    </div>
</div>

<?= /** @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false); ?>
