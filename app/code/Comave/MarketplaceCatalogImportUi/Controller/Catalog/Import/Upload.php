<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportUi\Controller\Catalog\Import;

use Comave\MarketplaceCatalogImport\Model\Command\AttributeValidator;
use Comave\MarketplaceCatalogImport\Model\Command\ConfigurableProductValidator;
use Comave\MarketplaceCatalogImport\Model\Command\TemplateDownloader;
use Comave\MarketplaceCatalogImportUi\Exception\InvalidRowException;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\UploaderFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\LayoutInterface;

class Upload implements HttpPostActionInterface, AccountInterface
{
    /**
     * @param UploaderFactory $uploaderFactory
     * @param Filesystem $filesystem
     * @param ResultFactory $resultFactory
     * @param RequestInterface $request
     * @param LayoutInterface $layout
     * @param DataPersistorInterface $dataPersistor
     * @param ConfigurableProductValidator $configurableProductValidator
     * @param CurrentCustomer $currentCustomer
     * @param AttributeValidator $attributeValidator
     */
    public function __construct(
        private readonly UploaderFactory $uploaderFactory,
        private readonly Filesystem $filesystem,
        private readonly ResultFactory $resultFactory,
        private readonly RequestInterface $request,
        private readonly LayoutInterface $layout,
        private readonly DataPersistorInterface $dataPersistor,
        private readonly ConfigurableProductValidator $configurableProductValidator,
        private readonly CurrentCustomer $currentCustomer,
        private readonly AttributeValidator $attributeValidator,
    ) {
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $fileInput = $this->request->getFiles('csv_file');
        $this->dataPersistor->clear($this->getImportedKey());
        $importedRows = [];
        $rowsWithErrors = [];

        try {
            if (empty($fileInput) || $fileInput['error'] != UPLOAD_ERR_OK) {
                throw new LocalizedException(__('No file uploaded or upload error.'));
            }

            $uploader = $this->uploaderFactory->create(['fileId' => 'csv_file']);
            $uploader->setAllowedExtensions(['csv']);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(false);

            // Save file temporarily to var/tmp
            $tmpDir = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath('tmp');
            $result = $uploader->save($tmpDir);

            if (!$result) {
                throw new LocalizedException(__('File cannot be saved.'));
            }

            $filePath = $tmpDir . DIRECTORY_SEPARATOR . $result['file'];

            // Validate CSV contents (basic example)
            $handle = fopen($filePath, 'r');
            if (!$handle) {
                throw new LocalizedException(__('Cannot open uploaded file.'));
            }

            $rowCount = 0;
            $errors = [];
            $requiredAttributes = TemplateDownloader::REQUIRED_ATTRIBUTES;
            $optionalAttributes = [];
            $formattedHeader = [];
            $encounteredSkus = [];
            $skuColumn = false;

            while (($row = fgetcsv($handle)) !== false) {
                if ($rowCount === 0) {
                    $this->processAttributes($row, $requiredAttributes, $optionalAttributes);
                    $formattedHeader = $requiredAttributes + $optionalAttributes;
                    $this->attributeValidator->loadAttributes($formattedHeader);
                    $skuColumn = array_search('sku', $formattedHeader);
                    $rowCount++;
                    continue;
                }

                foreach ($requiredAttributes as $attributeIndex => $attributeCode) {
                    if (strlen($row[$attributeIndex])) {
                        if (!$this->attributeValidator->validateAttributeValue($row[$attributeIndex], $attributeCode)) {
                            $errors[] = __('Invalid data for row %1, column %2', $rowCount, $attributeCode);
                            $rowsWithErrors[] = $rowCount;
                        }

                        continue;
                    }

                    $errors[] = __('Invalid data for row %1, column %2', $rowCount, $attributeCode);
                    $rowsWithErrors[] = $rowCount;
                }

                if (count($row) !== count($formattedHeader)) {
                    $errors[] = __(
                        'Invalid data for row %1, invalid column count, %2 in header, %3 in the row ',
                        $rowCount,
                        count($formattedHeader),
                        count($row)
                    );

                    $rowsWithErrors[] = $rowCount;
                }

                $importedRows[] = $row;

                if (in_array($row[$skuColumn], $encounteredSkus)) {
                    $errors[] = __('Invalid data for row %1, duplicate found, must be unique, column %2', $rowCount, 'sku');
                    $rowsWithErrors[] = $rowCount;
                }

                $encounteredSkus[] = $row[$skuColumn];
                $rowCount++;
            }

            $rowCount = count($importedRows);
            ksort($formattedHeader);

            array_unshift($importedRows, $formattedHeader);
            $this->configurableProductValidator->execute($importedRows);
            fclose($handle);
            @unlink($filePath);

            if (count($importedRows) <= 1) {
                $errors[] = __('Missing product data');
            }

            if (!empty($errors)) {
                return $resultJson->setData([
                    'success' => false,
                    'errors' => $errors,
                    'rowsHtml' => $this->getResultsBlock($importedRows, array_unique($rowsWithErrors))
                ]);
            }

            $this->dataPersistor->set($this->getImportedKey(), $importedRows);
            return $resultJson->setData([
                'success' => true,
                'rowsHtml' => $this->getResultsBlock($importedRows, array_unique($rowsWithErrors)),
                'message' => __('CSV file uploaded and validated successfully. Total rows: %1', $rowCount)
            ]);

        } catch (\Exception|InvalidRowException $e) {
            if ($e instanceof InvalidRowException) {
                $rowsWithErrors[] = $e->getRowNr();
            }

            return $resultJson->setData([
                'success' => false,
                'rowsHtml' => $this->getResultsBlock($importedRows, array_unique($rowsWithErrors)),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * @param array $headerRow
     * @param array $requiredAttributes
     * @param array $optionalAttributes
     * @return void
     * @throws LocalizedException
     */
    private function processAttributes(array $headerRow, array &$requiredAttributes, array &$optionalAttributes): void
    {
        $replaceVars = [
            ' (required)',
            ' (optional)'
        ];

        foreach ($headerRow as $i => $attributeName) {
            if (!str_contains($attributeName, 'required') && !str_contains($attributeName, 'optional')) {
                throw new LocalizedException(__('File headers should not be modified'));
            }

            if (str_contains($attributeName, 'required')) {
                $cleanedValue = str_replace($replaceVars, '', $attributeName);

                if (!in_array($cleanedValue, $requiredAttributes)) {
                    $requiredAttributes[$i] = $cleanedValue;
                }
            } else {
                $cleanedValue = str_replace($replaceVars, '', $attributeName);

                if (!in_array($cleanedValue, $optionalAttributes)) {
                    $optionalAttributes[$i] = $cleanedValue;
                }
            }
        }
    }

    /**
     * @param array $importedRows
     * @param array $rowsWithErrors
     * @return string
     */
    private function getResultsBlock(array $importedRows, array $rowsWithErrors = []): string
    {
        return $this->layout->createBlock(
            Template::class
            )->setNameInLayout('import.rows')
                ->setTemplate('Comave_MarketplaceCatalogImportUi::view/import/rows.phtml')
                ->setData('rows', $importedRows)
                ->setData('rowsWithErrors', $rowsWithErrors)
                ->toHtml();
    }

    /**
     * @return string
     */
    private function getImportedKey(): string
    {
        return sprintf('imported_products_%s', $this->currentCustomer->getCustomerId());
    }
}
