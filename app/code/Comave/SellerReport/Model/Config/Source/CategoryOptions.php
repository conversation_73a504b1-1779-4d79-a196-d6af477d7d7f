<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Comave\SellerReport\Service\CategoryReportService;

/**
 * Category options source for UI components
 * 
 * Provides formatted options for dropdowns and multiselects.
 * Business logic is handled by CategoryReportService.
 */
class CategoryOptions implements OptionSourceInterface
{
    private ?array $options = null;

    /**
     * @param CategoryReportService $categoryReportService
     */
    public function __construct(
        private readonly CategoryReportService $categoryReportService
    ) {
    }

    /**
     * Get options for category multi-select filter
     *
     * @return array Format: [['value' => 'id', 'label' => 'Name'], ...]
     */
    public function toOptionArray(): array
    {
        if ($this->options === null) {
            $this->options = $this->formatCategoriesAsOptions();
        }

        return $this->options;
    }

    /**
     * Clear cached options (delegates to service)
     *
     * @return void
     */
    public function clearCache(): void
    {
        $this->categoryReportService->clearCache();
        $this->options = null;
    }

    /**
     * Format category data as UI options
     *
     * @return array
     */
    private function formatCategoriesAsOptions(): array
    {
        $categories = $this->categoryReportService->getReportCategories();
        
        if (empty($categories)) {
            return [['value' => '', 'label' => __('No Categories Available')]];
        }

        $options = [['value' => '', 'label' => __('All Categories')]];

        foreach ($categories as $categoryId => $categoryName) {
            $options[] = [
                'value' => (string)$categoryId,
                'label' => $categoryName ?: 'Category #' . $categoryId
            ];
        }

        return $options;
    }
}

