<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\Publisher;

use Comave\SellerReport\Api\Data\ReportBatchInterface;
use Comave\SellerReport\Api\Data\ReportBatchInterfaceFactory;
use Magento\Framework\MessageQueue\PublisherInterface;
use Psr\Log\LoggerInterface;

/**
 * Publisher for category origin report messages
 */
class CategoryOriginReportPublisher
{
    private const string TOPIC_NAME = 'comave.reports.category.origin.generate';
    private const int BATCH_SIZE = 1000;

    /**
     * @param PublisherInterface $publisher
     * @param ReportBatchInterfaceFactory $batchFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly PublisherInterface $publisher,
        private readonly ReportBatchInterfaceFactory $batchFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Publish report generation batches to message queue
     *
     * @param string $reportDate
     * @param int $totalProducts
     * @return bool
     */
    public function publishBatches(string $reportDate, int $totalProducts): bool
    {
        $this->logger->info("[SellerReport Publisher] Publishing batches for {$totalProducts} products on {$reportDate}");
        
        try {
            $batchCount = 0;
            $startProductId = 1;
            
            while ($startProductId <= $totalProducts) {
                $endProductId = min($startProductId + self::BATCH_SIZE - 1, $totalProducts);
                $batchId = $this->generateBatchId($reportDate, $batchCount);
                
                $batch = $this->batchFactory->create();
                $batch->setBatchId($batchId)
                      ->setReportDate($reportDate)
                      ->setStartProductId($startProductId)
                      ->setEndProductId($endProductId)
                      ->setBatchSize($endProductId - $startProductId + 1);
                
                $this->publisher->publish(self::TOPIC_NAME, $batch);
                
                $startProductId = $endProductId + 1;
                $batchCount++;
            }
            
            $this->logger->info("[SellerReport Publisher] Successfully published {$batchCount} batches");
            return true;
            
        } catch (\Exception $e) {
            $this->logger->error("[SellerReport Publisher] Failed to publish batches: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Publish a single batch for immediate processing
     *
     * @param ReportBatchInterface $batch
     * @return bool
     */
    public function publishBatch(ReportBatchInterface $batch): bool
    {
        try {
            $this->publisher->publish(self::TOPIC_NAME, $batch);
            
            $this->logger->info("[SellerReport Publisher] Published single batch {$batch->getBatchId()}");
            return true;
            
        } catch (\Exception $e) {
            $this->logger->error("[SellerReport Publisher] Failed to publish batch: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique batch ID
     *
     * @param string $reportDate
     * @param int $batchNumber
     * @return string
     */
    private function generateBatchId(string $reportDate, int $batchNumber): string
    {
        return sprintf('category_origin_%s_batch_%04d', $reportDate, $batchNumber);
    }
} 