<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\Queue;

use Comave\SellerReport\Api\Data\ReportBatchInterface;
use Comave\SellerReport\Cron\GenerateCategorySellerOriginReport;
use Psr\Log\LoggerInterface;

/**
 * Message queue handler for category origin reports
 */
class CategoryOriginReportHandler
{
    /**
     * @param GenerateCategorySellerOriginReport $reportGenerator
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly GenerateCategorySellerOriginReport $reportGenerator,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process message from RabbitMQ queue
     *
     * @param ReportBatchInterface $batch
     * @return void
     */
    public function process(ReportBatchInterface $batch): void
    {
        $this->logger->info(sprintf(
            "[SellerReport] Processing batch %s for date %s (Products: %d-%d)",
            $batch->getBatchId(),
            $batch->getReportDate(),
            $batch->getStartProductId(),
            $batch->getEndProductId()
        ));

        try {
            // Skip cleanup since other batches might be running concurrently
            $result = $this->reportGenerator->execute(
                $batch->getStartProductId(),
                $batch->getEndProductId(),
                true
            );

            if ($result) {
                $this->logger->info("[SellerReport] Batch {$batch->getBatchId()} processed successfully");
            } else {
                $this->logger->warning("[SellerReport] Batch {$batch->getBatchId()} processed but no data generated");
            }

        } catch (\Exception $e) {
            $this->logger->error("[SellerReport] Failed to process batch {$batch->getBatchId()}: " . $e->getMessage());
            throw new \Exception("Failed to process report batch: " . $e->getMessage(), $e->getCode(), $e);
        }
    }
} 