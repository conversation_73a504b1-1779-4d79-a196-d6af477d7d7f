<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\Data;

use Magento\Framework\Serialize\SerializerInterface;
use Comave\SellerReport\Enum\SupportedCountries;

/**
 * Country data processor for flexible JSON-based country storage
 * 
 * Handles encoding/decoding and manipulation of country seller count data
 * stored in JSON format for scalability.
 */
class CountryDataProcessor
{
    /**
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * Initialize empty country data structure
     *
     * @return array
     */
    public function initializeCountryData(): array
    {
        $data = [
            'Other' => 0,
            'Unknown' => 0,
            'Total' => 0
        ];

        // Add all supported countries
        foreach (SupportedCountries::cases() as $country) {
            $data[$country->value] = 0;
        }

        return $data;
    }

    /**
     * Increment country count in data array
     *
     * @param array $countryData
     * @param string $countryCode
     * @return array
     */
    public function incrementCountry(array &$countryData, string $countryCode): array
    {
        if (!isset($countryData[$countryCode])) {
            $countryData[$countryCode] = 0;
        }
        
        $countryData[$countryCode]++;
        $countryData['Total']++;
        
        return $countryData;
    }

    /**
     * Encode country data as JSON string for database storage
     *
     * @param array $countryData
     * @return string
     */
    public function encodeCountryData(array $countryData): string
    {
        return $this->serializer->serialize($countryData);
    }

    /**
     * Decode JSON country data from database
     *
     * @param string $jsonData
     * @return array
     */
    public function decodeCountryData(string $jsonData): array
    {
        try {
            $data = $this->serializer->unserialize($jsonData);
            return is_array($data) ? $data : $this->initializeCountryData();
        } catch (\Exception $e) {
            return $this->initializeCountryData();
        }
    }

    /**
     * Get country count for specific country
     *
     * @param array $countryData
     * @param string $countryCode
     * @return int
     */
    public function getCountryCount(array $countryData, string $countryCode): int
    {
        return (int)($countryData[$countryCode] ?? 0);
    }

    /**
     * Get total count from country data
     *
     * @param array $countryData
     * @return int
     */
    public function getTotalCount(array $countryData): int
    {
        return (int)($countryData['Total'] ?? 0);
    }

    /**
     * Get all countries with counts > 0
     *
     * @param array $countryData
     * @return array Format: ['GB' => 10, 'NL' => 5, ...]
     */
    public function getActiveCountries(array $countryData): array
    {
        $active = [];
        foreach ($countryData as $country => $count) {
            if ($count > 0 && $country !== 'Total') {
                $active[$country] = $count;
            }
        }
        return $active;
    }

    /**
     * Merge multiple country data arrays
     *
     * @param array $datasets
     * @return array
     */
    public function mergeCountryData(array $datasets): array
    {
        $merged = $this->initializeCountryData();
        
        foreach ($datasets as $dataset) {
            foreach ($dataset as $country => $count) {
                if (!isset($merged[$country])) {
                    $merged[$country] = 0;
                }
                $merged[$country] += $count;
            }
        }
        
        // Recalculate total
        $merged['Total'] = array_sum(array_filter($merged, function($key) {
            return $key !== 'Total';
        }, ARRAY_FILTER_USE_KEY));
        
        return $merged;
    }

    /**
     * Validate country data structure
     *
     * @param array $countryData
     * @return bool
     */
    public function isValidCountryData(array $countryData): bool
    {
        // Must have Total field
        if (!isset($countryData['Total'])) {
            return false;
        }

        // All values must be numeric
        foreach ($countryData as $count) {
            if (!is_numeric($count)) {
                return false;
            }
        }

        return true;
    }
} 