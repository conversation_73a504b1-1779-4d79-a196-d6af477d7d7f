<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\Data;

use Comave\SellerReport\Api\Data\ReportBatchInterface;
use Magento\Framework\DataObject;

/**
 * Report batch data model
 */
class ReportBatch extends DataObject implements ReportBatchInterface
{
    /**
     * @inheritDoc
     */
    public function getBatchId(): string
    {
        return (string)$this->getData(self::BATCH_ID);
    }

    /**
     * @inheritDoc
     */
    public function setBatchId(string $batchId): ReportBatchInterface
    {
        return $this->setData(self::BATCH_ID, $batchId);
    }

    /**
     * @inheritDoc
     */
    public function getReportDate(): string
    {
        return (string)$this->getData(self::REPORT_DATE);
    }

    /**
     * @inheritDoc
     */
    public function setReportDate(string $reportDate): ReportBatchInterface
    {
        return $this->setData(self::REPORT_DATE, $reportDate);
    }

    /**
     * @inheritDoc
     */
    public function getStartProductId(): int
    {
        return (int)$this->getData(self::START_PRODUCT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setStartProductId(int $startProductId): ReportBatchInterface
    {
        return $this->setData(self::START_PRODUCT_ID, $startProductId);
    }

    /**
     * @inheritDoc
     */
    public function getEndProductId(): int
    {
        return (int)$this->getData(self::END_PRODUCT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setEndProductId(int $endProductId): ReportBatchInterface
    {
        return $this->setData(self::END_PRODUCT_ID, $endProductId);
    }

    /**
     * @inheritDoc
     */
    public function getBatchSize(): int
    {
        return (int)$this->getData(self::BATCH_SIZE);
    }

    /**
     * @inheritDoc
     */
    public function setBatchSize(int $batchSize): ReportBatchInterface
    {
        return $this->setData(self::BATCH_SIZE, $batchSize);
    }
} 