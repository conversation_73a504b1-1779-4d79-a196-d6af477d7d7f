<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Serialize\SerializerInterface;
use Comave\SellerReport\Model\Data\CountryDataProcessor;

/**
 * Category Origin Report Model
 */
class CategoryOriginReport extends AbstractModel
{
    /**
     * @param SerializerInterface $serializer
     * @param CountryDataProcessor $countryDataProcessor
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly CountryDataProcessor $countryDataProcessor,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Initialize model
     */
    protected function _construct(): void
    {
        $this->_init(\Comave\SellerReport\Model\ResourceModel\CategoryOriginReport::class);
    }

    /**
     * Get country data as array
     *
     * @return array
     */
    public function getCountryDataArray(): array
    {
        $jsonData = $this->getData('country_data');
        if (!$jsonData) {
            return [];
        }

        return $this->countryDataProcessor->decodeCountryData($jsonData);
    }

    /**
     * Set country data from array
     *
     * @param array $countryData
     * @return $this
     */
    public function setCountryDataArray(array $countryData): self
    {
        $jsonData = $this->countryDataProcessor->encodeCountryData($countryData);
        $this->setData('country_data', $jsonData);
        
        // Also update the total_count field
        $totalCount = $this->countryDataProcessor->getTotalCount($countryData);
        $this->setData('total_count', $totalCount);

        return $this;
    }

    /**
     * Get count for specific country
     *
     * @param string $countryCode
     * @return int
     */
    public function getCountryCount(string $countryCode): int
    {
        $countryData = $this->getCountryDataArray();
        return $this->countryDataProcessor->getCountryCount($countryData, $countryCode);
    }

    /**
     * Get all active countries (with count > 0)
     *
     * @return array
     */
    public function getActiveCountries(): array
    {
        $countryData = $this->getCountryDataArray();
        return $this->countryDataProcessor->getActiveCountries($countryData);
    }

    /**
     * Get formatted country breakdown for display
     *
     * @return string
     */
    public function getFormattedCountryBreakdown(): string
    {
        $activeCountries = $this->getActiveCountries();
        
        if (empty($activeCountries)) {
            return 'No data';
        }

        $breakdown = [];
        arsort($activeCountries); // Sort by count descending
        
        foreach ($activeCountries as $country => $count) {
            $breakdown[] = "{$country}: {$count}";
        }
        
        return implode(', ', $breakdown);
    }

    /**
     * Before save - ensure total_count is properly set
     *
     * @return $this
     */
    public function beforeSave(): self
    {
        parent::beforeSave();
        
        // Ensure total_count is always in sync with country_data
        if ($this->hasData('country_data') && !$this->hasData('total_count')) {
            $countryData = $this->getCountryDataArray();
            if (!empty($countryData)) {
                $totalCount = $this->countryDataProcessor->getTotalCount($countryData);
                $this->setData('total_count', $totalCount);
            }
        }

        return $this;
    }
}