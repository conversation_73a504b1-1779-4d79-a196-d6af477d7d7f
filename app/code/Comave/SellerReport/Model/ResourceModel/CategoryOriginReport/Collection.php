<?php

declare(strict_types=1);

namespace Comave\SellerReport\Model\ResourceModel\CategoryOriginReport;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Comave\SellerReport\Model\CategoryOriginReport;
use Comave\SellerReport\Model\ResourceModel\CategoryOriginReport as CategoryOriginReportResource;

/**
 * Collection for Category Origin Report data
 * 
 * Handles database collection operations for category seller origin reports.
 */
class Collection extends AbstractCollection
{
    /**
     * Define model and resource model
     */
    protected function _construct(): void
    {
        $this->_init(CategoryOriginReport::class, CategoryOriginReportResource::class);
    }
}