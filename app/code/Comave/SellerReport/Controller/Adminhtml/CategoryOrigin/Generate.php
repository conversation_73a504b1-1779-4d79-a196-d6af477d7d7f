<?php

declare(strict_types=1);

namespace Comave\SellerReport\Controller\Adminhtml\CategoryOrigin;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Comave\SellerReport\Cron\GenerateCategorySellerOriginReport;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultInterface;

/**
 * Controller for generating category seller origin reports
 */
class Generate extends Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_SellerReport::category_origin_report';

    /**
     * @param Context $context
     * @param GenerateCategorySellerOriginReport $reportGenerator
     */
    public function __construct(
        Context $context,
        private readonly GenerateCategorySellerOriginReport $reportGenerator,
    ) {
        parent::__construct($context);
    }

    /**
     * Execute the report generation
     *
     * @return ResultInterface
     */
    public function execute(): ResultInterface
    {
        try {
            $result = $this->reportGenerator->execute();
            
            if ($result) {
                $this->messageManager->addSuccessMessage(__('The seller report has been successfully generated.'));
            } else {
                $this->messageManager->addWarningMessage(__('The report was processed but no data was generated.'));
            }
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while generating the report: %1', $e->getMessage()));
        }

        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('*/*/report');
    }
}
