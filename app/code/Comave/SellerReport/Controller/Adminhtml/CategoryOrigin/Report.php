<?php

declare(strict_types=1);

namespace Comave\SellerReport\Controller\Adminhtml\CategoryOrigin;

use Magento\Backend\App\Action;
use Magento\Framework\View\Result\PageFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\View\Result\Page;

/**
 * Controller for displaying category seller origin reports
 */
class Report extends Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_SellerReport::category_origin_report';

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
    }

    /**
     * Execute the report display action
     *
     * @return Page
     */
    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->prepend(__('Live Report'));
        return $resultPage;
    }
}