<?php

declare(strict_types=1);

namespace Comave\SellerReport\Cron;

use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory as SellerCollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as MpProductCollectionFactory;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Comave\SellerReport\Enum\SupportedCountries;
use Comave\SellerReport\Service\CategoryReportService;
use Comave\SellerReport\Model\Data\CountryDataProcessor;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;

/**
 * Generate Category Seller Origin Report with optimized batch processing
 * 
 * Handles large datasets efficiently using pagination and batch operations
 * to prevent memory issues and database timeouts.
 */
class GenerateCategorySellerOriginReport
{
    private const string OTHER = 'Other';
    private const string UNKNOWN = 'Unknown';
    private const string TOTAL = 'Total';
    
    // Performance configuration
    private const int BATCH_SIZE = 1000;
    private const int DELETE_BATCH_SIZE = 500;
    private const int INSERT_BATCH_SIZE = 100;

    /**
     * @param ResourceConnection $resource
     * @param LoggerInterface $logger
     * @param CategoryRepositoryInterface $categoryRepository
     * @param SellerCollectionFactory $sellerCollectionFactory
     * @param MpProductCollectionFactory $mpProductCollectionFactory
     * @param ProductCollectionFactory $productCollectionFactory
     * @param CategoryReportService $categoryReportService
     * @param CountryDataProcessor $countryDataProcessor
     */
    public function __construct(
        private readonly ResourceConnection $resource,
        private readonly LoggerInterface $logger,
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly SellerCollectionFactory $sellerCollectionFactory,
        private readonly MpProductCollectionFactory $mpProductCollectionFactory,
        private readonly ProductCollectionFactory $productCollectionFactory,
        private readonly CategoryReportService $categoryReportService,
        private readonly CountryDataProcessor $countryDataProcessor,
    ) {
    }

    /**
     * Execute the report generation with batch processing
     *
     * @param int|null $startProductId Start of product range for batch processing
     * @param int|null $endProductId End of product range for batch processing  
     * @param bool $skipCleanup Skip data cleanup for batch processing
     * @return bool Success status
     */
    public function execute(?int $startProductId = null, ?int $endProductId = null, bool $skipCleanup = false): bool
    {
        $isBatchMode = $startProductId !== null && $endProductId !== null;
        $mode = $isBatchMode ? "BATCH ({$startProductId}-{$endProductId})" : "FULL";
        
        $this->logger->info("[SellerReport] Starting report generation - Mode: {$mode}");

        $connection = $this->resource->getConnection();
        $reportTable = $this->resource->getTableName('comave_category_seller_origin_report');
        $reportDate = date('Y-m-d');

        if (!$connection->isTableExists($reportTable)) {
            $this->logger->critical("[SellerReport] Table {$reportTable} does not exist.");
            return false;
        }

        try {
            if (!$skipCleanup) {
                $this->cleanExistingData($connection, $reportTable, $reportDate);
            }
            
            $sellerMap = $this->getSellerCountries();
            $productMap = $this->getProductSellers();
            
            if ($isBatchMode) {
                $totalProcessed = $this->processProductBatch($connection, $reportTable, $reportDate, $sellerMap, $productMap, $startProductId, $endProductId);
            } else {
                $totalProcessed = $this->processProductsInBatches($connection, $reportTable, $reportDate, $sellerMap, $productMap);
                $this->categoryReportService->clearCache();
            }
            
            $this->logger->info("[SellerReport] Completed successfully - Processed: {$totalProcessed} products");
            return true;
            
        } catch (\Exception $e) {
            $this->logger->critical("[SellerReport] Error during report generation: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean existing data using batched deletes to prevent database locks
     */
    private function cleanExistingData($connection, string $reportTable, string $reportDate): void
    {
        $this->logger->info("[SellerReport] Cleaning existing data for date: {$reportDate}");
        
        $select = $connection->select()
            ->from($reportTable, ['report_id'])
            ->where('report_date = ?', $reportDate)
            ->limit(self::DELETE_BATCH_SIZE);

        $deletedTotal = 0;
        do {
            $ids = $connection->fetchCol($select);
            if (!empty($ids)) {
                $connection->delete($reportTable, ['report_id IN (?)' => $ids]);
                $deletedTotal += count($ids);
                
                if ($deletedTotal % (self::DELETE_BATCH_SIZE * 5) === 0) {
                    gc_collect_cycles();
                }
            }
        } while (!empty($ids));
        
        if ($deletedTotal > 0) {
            $this->logger->info("[SellerReport] Cleaned {$deletedTotal} existing records");
        }
    }

    /**
     * Process products in batches to prevent memory issues
     */
    private function processProductsInBatches($connection, string $reportTable, string $reportDate, array $sellerMap, array $productMap): int
    {
        $page = 1;
        $totalProcessed = 0;
        $results = [];
        $nameCache = [];
        
        do {
            $products = $this->getProductBatch($page, self::BATCH_SIZE);
            $batchSize = $products->getSize();
            
            if ($batchSize === 0) {
                break;
            }
            
            foreach ($products as $product) {
                $this->processProduct($product, $sellerMap, $productMap, $results, $nameCache);
                $totalProcessed++;
                
                if ($totalProcessed % 100 === 0) {
                    $product = null;
                }
            }
            
            if (count($results) >= self::INSERT_BATCH_SIZE) {
                $this->insertBatch($connection, $reportTable, $reportDate, $results);
                $results = [];
                gc_collect_cycles();
            }
            
            $products->clear();
            unset($products);
            $page++;
            
        } while ($batchSize === self::BATCH_SIZE);
        
        if (!empty($results)) {
            $this->insertBatch($connection, $reportTable, $reportDate, $results);
        }
        
        return $totalProcessed;
    }

    /**
     * Process a specific range of products for message queue batch processing
     */
    private function processProductBatch($connection, string $reportTable, string $reportDate, array $sellerMap, array $productMap, int $startProductId, int $endProductId): int
    {
        $results = [];
        $nameCache = [];
        $totalProcessed = 0;
        
        $products = $this->productCollectionFactory->create()
            ->addAttributeToSelect('category_ids')
            ->addAttributeToFilter('status', Status::STATUS_ENABLED)
            ->addAttributeToFilter('visibility', ['neq' => Visibility::VISIBILITY_NOT_VISIBLE])
            ->addFieldToFilter('entity_id', ['from' => $startProductId, 'to' => $endProductId]);
        
        foreach ($products as $product) {
            $this->processProduct($product, $sellerMap, $productMap, $results, $nameCache);
            $totalProcessed++;
        }
        
        if (!empty($results)) {
            $this->insertBatch($connection, $reportTable, $reportDate, $results);
        }
        
        return $totalProcessed;
    }

    /**
     * Get a batch of products with pagination
     */
    private function getProductBatch(int $page, int $batchSize)
    {
        return $this->productCollectionFactory->create()
            ->addAttributeToSelect('category_ids')
            ->addAttributeToFilter('status', Status::STATUS_ENABLED)
            ->addAttributeToFilter('visibility', ['neq' => Visibility::VISIBILITY_NOT_VISIBLE])
            ->setPageSize($batchSize)
            ->setCurPage($page);
    }

    /**
     * Process a single product and update results
     */
    private function processProduct($product, array $sellerMap, array $productMap, array &$results, array &$nameCache): void
    {
        $productId = (int)$product->getId();
        if (!isset($productMap[$productId])) {
            return;
        }

        $sellerId = $productMap[$productId];
        $origin = $this->getSellerOrigin($sellerMap, $sellerId);

        foreach ((array)$product->getCategoryIds() as $categoryId) {
            $categoryId = (int)$categoryId;
            $categoryName = $this->getCategoryName($nameCache, $categoryId);
            
            $results[$categoryId] = $results[$categoryId] ?? $this->initializeCategoryData($categoryId, $categoryName);
            $this->incrementCategoryData($results[$categoryId], $origin);
        }
    }

    /**
     * Insert a batch of results into the database
     */
    private function insertBatch($connection, string $reportTable, string $reportDate, array &$results): void
    {
        if (empty($results)) {
            return;
        }

        $batch = [];
        foreach ($results as $row) {
            $countryData = $row['country_data'];
            $record = [
                'report_date' => $reportDate,
                'category_id' => $row['category_id'],
                'category_name' => $row['category_name'],
                'country_data' => $this->countryDataProcessor->encodeCountryData($countryData),
                'total_count' => $this->countryDataProcessor->getTotalCount($countryData),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            $batch[] = $record;
        }

        $connection->beginTransaction();
        try {
            $connection->insertMultiple($reportTable, $batch);
            $connection->commit();
        } catch (\Exception $e) {
            $connection->rollBack();
            $this->logger->error("[SellerReport] Error during batch insert: " . $e->getMessage());
            throw $e;
        }
    }

    private function getSellerOrigin(array $sellerMap, int $sellerId): string
    {
        $origin = $sellerMap[$sellerId] ?? self::UNKNOWN;
        
        // Handle Unknown as a special case
        if ($origin === self::UNKNOWN) {
            return self::UNKNOWN;
        }
        
        // Try to map to supported countries, fallback to Other for unsupported countries
        return SupportedCountries::tryFromField($origin)?->value ?? self::OTHER;
    }

    private function getCategoryName(array &$nameCache, int $categoryId): string
    {
        if (!isset($nameCache[$categoryId])) {
            try {
                // Load category name directly from database to bypass attribute backend issues
                $connection = $this->resource->getConnection();
                $query = $connection->select()
                    ->from(['c' => $connection->getTableName('catalog_category_entity')], [])
                    ->join(
                        ['v' => $connection->getTableName('catalog_category_entity_varchar')],
                        'c.row_id = v.row_id',
                        ['value']
                    )
                    ->join(
                        ['a' => $connection->getTableName('eav_attribute')],
                        'v.attribute_id = a.attribute_id',
                        []
                    )
                    ->where('c.entity_id = ?', $categoryId)
                    ->where('a.attribute_code = ?', 'name')
                    ->where('v.store_id = ?', 0) // Default store
                    ->limit(1);

                $categoryName = $connection->fetchOne($query);
                $nameCache[$categoryId] = $categoryName ?: "Category #$categoryId";
            } catch (\Exception $e) {
                $this->logger->warning("[SellerReport] Failed to load category name for ID {$categoryId}: " . $e->getMessage());
                $nameCache[$categoryId] = "Category #$categoryId";
            }
        }

        return $nameCache[$categoryId];
    }

    private function initializeCategoryData(int $categoryId, string $categoryName): array
    {
        return [
            'category_id' => $categoryId,
            'category_name' => $categoryName,
            'country_data' => $this->countryDataProcessor->initializeCountryData(),
        ];
    }

    private function incrementCategoryData(array &$categoryData, string $origin): void
    {
        $this->countryDataProcessor->incrementCountry($categoryData['country_data'], $origin);
    }

    private function getSellerCountries(): array
    {
        $map = [];
        try {
            $collection = $this->sellerCollectionFactory->create()
                ->addFieldToFilter('is_seller', 1)
                ->addFieldToSelect(['seller_id', 'country_pic']);

            foreach ($collection as $seller) {
                $sellerId = (int)$seller->getData('seller_id');
                $countryCode = $seller->getData('country_pic') ?: self::UNKNOWN;
                
                if ($countryCode === self::UNKNOWN) {
                    $map[$sellerId] = self::UNKNOWN;
                } elseif (SupportedCountries::tryFromField($countryCode) === null) {
                    $map[$sellerId] = self::OTHER;
                } else {
                    $countryValue = SupportedCountries::tryFromField($countryCode)->value;
                    $map[$sellerId] = $countryValue;
                }
            }
        } catch (\Exception $e) {
            $this->logger->error("[SellerReport] Failed to load sellers: " . $e->getMessage());
        }
        return $map;
    }

    private function getProductSellers(): array
    {
        $map = [];
        try {
            $collection = $this->mpProductCollectionFactory->create()
                ->addFieldToSelect(['mageproduct_id', 'seller_id']);

            foreach ($collection as $item) {
                $map[(int)$item->getData('mageproduct_id')] = (int)$item->getData('seller_id');
            }
        } catch (\Exception $e) {
            $this->logger->error("[SellerReport] Failed to load marketplace products: " . $e->getMessage());
        }
        return $map;
    }
}
