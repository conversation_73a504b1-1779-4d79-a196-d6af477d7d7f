<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="default">
        <job name="sellerreport_generate_daily" instance="Comave\SellerReport\Cron\GenerateDailyReport" method="execute">
            <schedule>0 2 * * *</schedule>
        </job>
        <job name="sellerreport_generate_category_seller_origin"
            instance="Comave\SellerReport\Cron\GenerateCategorySellerOriginReport"
            method="execute">
            <schedule>0 2 * * *</schedule>
        </job>    
    </group>
</config>
