<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Reports::report">
                    <resource id="Comave_SellerReport::seller_report" title="Seller Reports" sortOrder="50">
                        <resource id="Comave_SellerReport::sellers_by_country" title="Sellers by Country" sortOrder="10" />
                        <resource id="Comave_SellerReport::category_origin_report" title="Category x Seller Origin Report" />
                    </resource>                    
                </resource>
            </resource>
        </resources>
    </acl>
</config>