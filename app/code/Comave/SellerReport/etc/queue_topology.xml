<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    
    <exchange name="comave.reports.category.origin" type="topic" connection="amqp">
        <binding id="categoryOriginReportBinding"
                 topic="comave.reports.category.origin.generate"
                 destinationType="queue"
                 destination="comave.reports.category.origin.generate"/>
    </exchange>
    
</config> 