<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    
    <consumer name="comave.reports.category.origin" 
              queue="comave.reports.category.origin.generate" 
              connection="amqp" 
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\SellerReport\Model\Queue\CategoryOriginReportHandler::process" />
              
</config> 