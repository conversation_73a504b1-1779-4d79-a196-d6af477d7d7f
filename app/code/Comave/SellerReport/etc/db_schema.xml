<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
        
    <table name="comave_seller_report" resource="default" engine="innodb" comment="Comave Seller Country Snapshot Report">
        <column xsi:type="int" name="report_id" unsigned="true" nullable="false" identity="true" comment="Report ID"/>
        <column xsi:type="date" name="report_date" nullable="false" comment="Report Snapshot Date"/>
        <column xsi:type="varchar" name="country" length="20" nullable="false" comment="Country Code (ISO)"/>
        <column xsi:type="int" name="live_sellers" unsigned="true" nullable="false" default="0" comment="Live Sellers Count"/>
        <column xsi:type="int" name="not_live_sellers" unsigned="true" nullable="false" default="0" comment="Not Live Sellers Count"/>
        <column xsi:type="timestamp" name="created_at" nullable="false" default="CURRENT_TIMESTAMP" on_update="false" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" nullable="false" default="CURRENT_TIMESTAMP" on_update="true" comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="report_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_REPORT_DATE_COUNTRY_UNIQ">
            <column name="report_date"/>
            <column name="country"/>
        </constraint>
        <index referenceId="COMAVE_SELLER_REPORT_COUNTRY_IDX" indexType="btree">
            <column name="country"/>
        </index>
    </table>
    
    <table name="comave_category_seller_origin_report" resource="default" engine="innodb" comment="Category &amp; Seller Origin Report">
        <column name="report_id" xsi:type="int" unsigned="true" identity="true" nullable="false" comment="Primary Key"/>
        <column name="report_date" xsi:type="date" nullable="false" comment="Report Date"/>
        <column name="category_id" xsi:type="int" unsigned="true" nullable="false" comment="Category ID"/>
        <column name="category_name" xsi:type="varchar" length="255" nullable="true" comment="Category Name"/>
        <column name="country_data" xsi:type="json" nullable="false" comment="Country seller counts in JSON format"/>
        <column name="total_count" xsi:type="int" unsigned="true" nullable="false" default="0" comment="Total Seller Count (derived from JSON)"/>
        <column name="created_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP" on_update="false" comment="Created At"/>
        <column name="updated_at" xsi:type="timestamp" nullable="false" default="CURRENT_TIMESTAMP" on_update="true" comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="report_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_CAT_ORIGIN_UNIQ">
            <column name="report_date"/>
            <column name="category_id"/>
        </constraint>
        <index indexType="btree" referenceId="COMAVE_CAT_ORIGIN_IDX">
            <column name="category_id"/>
        </index>
        <index indexType="btree" referenceId="COMAVE_CAT_ORIGIN_DATE_IDX">
            <column name="report_date"/>
        </index>
    </table>
    
</schema>
