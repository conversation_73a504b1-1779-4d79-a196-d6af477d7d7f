<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- ReportBatch Data Interface Implementation -->
    <preference for="Comave\SellerReport\Api\Data\ReportBatchInterface" 
                type="Comave\SellerReport\Model\Data\ReportBatch" />
    
    <!-- Message Queue Merger Configuration -->
    <type name="Magento\Framework\MessageQueue\MergerFactory">
        <arguments>
            <argument name="mergers" xsi:type="array">
                <item name="ComaveReportBatchMerger" xsi:type="string">Magento\Framework\MessageQueue\Merger</item>
            </argument>
        </arguments>
    </type>
    
    <!-- Service Configuration -->
    <type name="Comave\SellerReport\Service\CategoryReportService">
        <arguments>
            <argument name="cache" xsi:type="object">Magento\Framework\App\Cache</argument>
        </arguments>
    </type>

    <!-- UI Grid Configuration -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="comave_seller_report_listing_data_source" xsi:type="string">Comave\SellerReport\Model\ResourceModel\SellerReport\Grid\Collection</item>
                <item name="comave_category_origin_report_listing_data_source" xsi:type="string">Comave\SellerReport\Model\ResourceModel\CategoryOriginReport\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="Comave\SellerReport\Model\ResourceModel\SellerReport\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_seller_report</argument>
            <argument name="resourceModel" xsi:type="string">Comave\SellerReport\Model\ResourceModel\SellerReport</argument>
        </arguments>
    </virtualType>

    <virtualType name="Comave\SellerReport\Model\ResourceModel\CategoryOriginReport\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_category_seller_origin_report</argument>
            <argument name="resourceModel" xsi:type="string">Comave\SellerReport\Model\ResourceModel\CategoryOriginReport</argument>
        </arguments>
    </virtualType>

    <virtualType name="ComavePivotedCategoryOriginReporting" type="Magento\Framework\View\Element\UiComponent\DataProvider\Reporting">
        <arguments>
            <argument name="collection" xsi:type="object">Comave\SellerReport\Model\ResourceModel\CategoryOriginReport\Collection</argument>
        </arguments>
    </virtualType>

    <!-- Cron Configuration -->
    <type name="Comave\SellerReport\Cron\GenerateDailyReport">
        <arguments>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>

</config>
