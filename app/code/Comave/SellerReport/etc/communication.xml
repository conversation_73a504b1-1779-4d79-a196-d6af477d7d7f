<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    
    <topic name="comave.reports.category.origin.generate" 
           request="Comave\SellerReport\Api\Data\ReportBatchInterface">
        <handler name="categoryOriginReportHandler" 
                 type="Comave\SellerReport\Model\Queue\CategoryOriginReportHandler" 
                 method="process" />
    </topic>
    
</config> 