<?php

declare(strict_types=1);

namespace Comave\SellerReport\Api\Data;

/**
 * Report batch interface for message queue processing
 */
interface ReportBatchInterface
{
    public const string BATCH_ID = 'batch_id';
    public const string REPORT_DATE = 'report_date';
    public const string START_PRODUCT_ID = 'start_product_id';
    public const string END_PRODUCT_ID = 'end_product_id';
    public const string BATCH_SIZE = 'batch_size';

    /**
     * Get batch ID
     *
     * @return string
     */
    public function getBatchId(): string;

    /**
     * Set batch ID
     *
     * @param string $batchId
     * @return $this
     */
    public function setBatchId(string $batchId): self;

    /**
     * Get report date
     *
     * @return string
     */
    public function getReportDate(): string;

    /**
     * Set report date
     *
     * @param string $reportDate
     * @return $this
     */
    public function setReportDate(string $reportDate): self;

    /**
     * Get start product ID for this batch
     *
     * @return int
     */
    public function getStartProductId(): int;

    /**
     * Set start product ID for this batch
     *
     * @param int $startProductId
     * @return $this
     */
    public function setStartProductId(int $startProductId): self;

    /**
     * Get end product ID for this batch
     *
     * @return int
     */
    public function getEndProductId(): int;

    /**
     * Set end product ID for this batch
     *
     * @param int $endProductId
     * @return $this
     */
    public function setEndProductId(int $endProductId): self;

    /**
     * Get batch size
     *
     * @return int
     */
    public function getBatchSize(): int;

    /**
     * Set batch size
     *
     * @param int $batchSize
     * @return $this
     */
    public function setBatchSize(int $batchSize): self;
} 