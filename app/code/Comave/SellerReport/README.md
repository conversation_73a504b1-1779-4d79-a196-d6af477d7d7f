# Comave SellerReport Module

## Overview

The SellerReport module generates comprehensive reports about product distribution across categories, broken down by seller origin countries. This module helps analyze marketplace product distribution patterns and seller geographical representation.

## Features

- **Category-Seller Origin Report**: Generates daily reports showing product counts per category, organized by seller origin countries
- **Automated Report Generation**: Runs automatically via cron job to ensure up-to-date data
- **Country-based Analytics**: Tracks products from supported countries (GB, Netherlands, Italy, Spain, France) plus "Other" and "Unknown" categories
- **Database Storage**: Stores reports in dedicated database table for historical analysis

## How It Works

The module processes:
1. **Active Products**: Only considers enabled and visible products
2. **Marketplace Sellers**: Maps products to their respective sellers
3. **Seller Origins**: Determines seller country based on seller profile data
4. **Category Assignment**: Tracks products across all assigned categories
5. **Data Aggregation**: Counts products per category-country combination

## Supported Countries

The module uses the `SupportedCountries` enum with standardized country codes:
- **GB** (Great Britain) - Database column: `GB`
- **NETHERLANDS** (Netherlands) - Database column: `NL`
- **ITALY** (Italy) - Database column: `IT`
- **SPAIN** (Spain) - Database column: `ES`
- **FRANCE** (France) - Database column: `FR`

Special categories are defined as constants:
- **OTHER** - For any other countries
- **UNKNOWN** - For sellers without country information  
- **TOTAL** - Total count across all countries

## Cron Schedule

The report generation runs automatically via cron job:
- **Cron Job**: `GenerateCategorySellerOriginReport::execute()`
- **Frequency**: Daily (schedule defined in crontab.xml)
- **Time**: Typically runs during off-peak hours
- **Data**: Replaces previous day's data to ensure accuracy

## Database Structure

Reports are stored in the `comave_category_seller_origin_report` table with columns:
- `report_date`: Date of the report
- `category_id`: Magento category ID
- `category_name`: Category display name
- Country columns (GB, NL, IT, ES, FR): Product counts per country
- `Other`: Products from unsupported countries
- `Unknown`: Products from sellers without country data
- `Total`: Total products in the category
- Timestamps: `created_at`, `updated_at`

## Dependencies

- **Magento Framework**: Core Magento functionality
- **Webkul Marketplace**: For marketplace seller and product data
- **Magento Catalog**: For product and category management

## Logging

The module logs its activities with `[SellerReport]` prefix:
- Info: Successful report generation
- Warning: No data to process
- Critical: Database errors or missing tables
- Error: Data loading failures

Check logs in: `var/log/system.log` 