<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         name="comave_category_origin_report_listing">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">comave_category_origin_report_listing.comave_category_origin_report_listing_data_source</item>
            <item name="deps" xsi:type="string">comave_category_origin_report_listing.comave_category_origin_report_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">category_origin_report_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="generate" xsi:type="array">
                <item name="name" xsi:type="string">generate_report</item>
                <item name="label" xsi:type="string" translate="true">Generate Today's Report</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">comave_report/categoryorigin/generate</item>
            </item>
        </item>
    </argument>

    <dataSource name="comave_category_origin_report_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">comave_category_origin_report_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">report_id</argument>
            <argument name="requestFieldName" xsi:type="string">report_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">report_id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <settings>
            <updateUrl path="mui/index/render"/>
        </settings>
    </dataSource>

    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <paging name="listing_paging"/>
        <exportButton name="export_button">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">comave_category_origin_report_listing.comave_category_origin_report_listing.category_origin_report_columns.ids</item>
                    <item name="options" xsi:type="array">
                        <item name="csv" xsi:type="array">
                            <item name="value" xsi:type="string">csv</item>
                            <item name="label" xsi:type="string" translate="true">CSV</item>
                            <item name="url" xsi:type="string">mui/export/gridToCsv</item>
                        </item>
                    </item>
                </item>
            </argument>
        </exportButton>
    </listingToolbar>

    <columns name="category_origin_report_columns">
        <selectionsColumn name="ids" sortOrder="110">
            <settings>
                <indexField>report_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>55</resizeDefaultWidth>
            </settings>
        </selectionsColumn>        
        
        <column name="report_date" sortOrder="10" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <label translate="true">Report Date</label>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <dateFormat>yyyy-MM-dd</dateFormat>
                <timezone>true</timezone>
                <sorting>desc</sorting>
            </settings>
        </column>

        <column name="category_name" sortOrder="20">
            <settings>
                <label translate="true">Category</label>
                <filter>select</filter>
                <options class="Comave\SellerReport\Model\Config\Source\CategoryOptions"/>
                <dataType>select</dataType>
                <sortable>true</sortable>
            </settings>
        </column>

        <column name="country_breakdown" sortOrder="30" class="Comave\SellerReport\Ui\Component\Listing\Column\CountryData">
            <settings>
                <label translate="true">Country Breakdown</label>
                <filter>false</filter>
                <sortable>false</sortable>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <resizeEnabled>true</resizeEnabled>
                <resizeDefaultWidth>300</resizeDefaultWidth>
            </settings>
        </column>

        <column name="total_count" sortOrder="40">
            <settings>
                <label translate="true">Total Sellers</label>
                <filter>false</filter>
                <dataType>number</dataType>
                <sortable>true</sortable>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>120</resizeDefaultWidth>
            </settings>
        </column>
    </columns>
</listing>
