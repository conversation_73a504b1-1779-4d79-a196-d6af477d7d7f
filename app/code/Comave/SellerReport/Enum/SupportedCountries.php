<?php
declare(strict_types=1);

namespace Comave\SellerReport\Enum;

enum SupportedCountries: string
{
    case GB = 'GB';
    case NETHERLANDS = 'NL';
    case ITALY = 'IT';
    case SPAIN = 'ES';
    case FRANCE = 'FR';

    public static function tryFromField(string $field): ?self
    {
        return self::tryFrom($field);
    }

    /**
     * Get human-readable name for the country
     */
    public function getReadableName(): string
    {
        return match($this) {
            self::GB => 'Great Britain',
            self::NETHERLANDS => 'Netherlands',
            self::ITALY => 'Italy',
            self::SPAIN => 'Spain',
            self::FRANCE => 'France',
        };
    }
}