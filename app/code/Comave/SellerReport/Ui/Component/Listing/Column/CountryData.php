<?php

declare(strict_types=1);

namespace Comave\SellerReport\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\Serialize\SerializerInterface;
use Comave\SellerReport\Model\Data\CountryDataProcessor;
use Comave\SellerReport\Enum\SupportedCountries;

/**
 * Custom column for displaying dynamic JSON country data
 * 
 * Renders country seller counts from JSON data in a user-friendly format
 * that scales automatically with any number of countries.
 */
class CountryData extends Column
{
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param SerializerInterface $serializer
     * @param CountryDataProcessor $countryDataProcessor
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        private readonly SerializerInterface $serializer,
        private readonly CountryDataProcessor $countryDataProcessor,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare data source with formatted country data
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                if (isset($item['country_data'])) {
                    $item[$this->getData('name')] = $this->formatCountryData($item['country_data']);
                }
            }
        }

        return $dataSource;
    }

    /**
     * Format JSON country data for display
     *
     * @param string $jsonData
     * @return string
     */
    private function formatCountryData(string $jsonData): string
    {
        try {
            $countryData = $this->countryDataProcessor->decodeCountryData($jsonData);
            $activeCountries = $this->countryDataProcessor->getActiveCountries($countryData);
            
            if (empty($activeCountries)) {
                return '<span class="text-muted">No data</span>';
            }

            $output = '<div class="country-breakdown">';
            
            // Sort countries by count (descending)
            arsort($activeCountries);
            
            foreach ($activeCountries as $country => $count) {
                $output .= sprintf(
                    '<div class="country-item"><strong>%s:</strong> %d</div>',
                    $this->formatCountryName($country),
                    $count
                );
            }
            
            $output .= '</div>';
            
            return $output;
            
        } catch (\Exception $e) {
            return '<span class="text-danger">Invalid data</span>';
        }
    }

    /**
     * Format country code to readable name
     *
     * @param string $countryCode
     * @return string
     */
    private function formatCountryName(string $countryCode): string
    {
        // Try to get from supported countries enum
        $supportedCountry = SupportedCountries::tryFromField($countryCode);
        if ($supportedCountry) {
            return $supportedCountry->getReadableName();
        }

        // Handle special cases
        return match($countryCode) {
            'Other' => 'Other',
            'Unknown' => 'Unknown',
            default => $countryCode
        };
    }
} 