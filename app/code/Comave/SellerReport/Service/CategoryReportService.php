<?php

declare(strict_types=1);

namespace Comave\SellerReport\Service;

use Magento\Framework\App\ResourceConnection;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as CategoryCollectionFactory;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for managing category data in reports
 */
class CategoryReportService
{
    private const string CACHE_KEY = 'comave_category_report_data';
    private const int CACHE_LIFETIME = 3600; // 1 hour in seconds
    private const array CACHE_TAGS = ['COMAVE_REPORTS_CACHE'];

    /**
     * @param ResourceConnection $resourceConnection
     * @param CategoryCollectionFactory $categoryCollectionFactory
     * @param CacheInterface $cache
     * @param SerializerInterface $serializer
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly CategoryCollectionFactory $categoryCollectionFactory,
        private readonly CacheInterface $cache,
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Get categories used in reports with caching
     *
     * @return array Format: ['category_id' => 'category_name', ...]
     */
    public function getReportCategories(): array
    {
        $cacheData = $this->cache->load(self::CACHE_KEY);

        if ($cacheData) {
            $this->logger->debug('[CategoryReportService] Loading categories from cache');
            return $this->serializer->unserialize($cacheData);
        }

        $categories = $this->loadCategoriesFromReportTable();
        
        if (empty($categories)) {
            $this->logger->info('[CategoryReportService] No report data found, using fallback categories');
            $categories = $this->getFallbackCategories();
        }

        $this->cacheCategories($categories);
        return $categories;
    }

    /**
     * Get all active store categories as fallback
     *
     * @return array Format: ['category_id' => 'category_name', ...]
     */
    public function getFallbackCategories(): array
    {
        try {
            $this->logger->debug('[CategoryReportService] Loading fallback categories from store');
            
            $categories = $this->categoryCollectionFactory->create()
                ->addAttributeToSelect('name')
                ->addIsActiveFilter();

            $result = [];
            foreach ($categories as $category) {
                $result[$category->getId()] = $category->getName();
            }

            $this->logger->info('[CategoryReportService] Loaded ' . count($result) . ' fallback categories');
            return $result;

        } catch (\Exception $e) {
            $this->logger->error('[CategoryReportService] Failed to load fallback categories: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Clear category cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        $this->logger->info('[CategoryReportService] Clearing category cache');
        $this->cache->remove(self::CACHE_KEY);
    }

    /**
     * Refresh category data and cache
     *
     * @return array
     */
    public function refreshCategories(): array
    {
        $this->clearCache();
        return $this->getReportCategories();
    }

    /**
     * Check if report table has category data
     *
     * @return bool
     */
    public function hasReportData(): bool
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $reportTable = $this->resourceConnection->getTableName('comave_category_seller_origin_report');
            
            if (!$connection->isTableExists($reportTable)) {
                return false;
            }

            $count = $connection->fetchOne(
                $connection->select()
                    ->from($reportTable, ['COUNT(*)'])
                    ->limit(1)
            );

            return (int)$count > 0;

        } catch (\Exception $e) {
            $this->logger->error('[CategoryReportService] Error checking report data: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Load categories from the report table
     *
     * @return array Format: ['category_id' => 'category_name', ...]
     */
    private function loadCategoriesFromReportTable(): array
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $reportTable = $this->resourceConnection->getTableName('comave_category_seller_origin_report');

            if (!$connection->isTableExists($reportTable)) {
                $this->logger->warning('[CategoryReportService] Report table does not exist');
                return [];
            }

            $this->logger->debug('[CategoryReportService] Loading categories from report table');

            $categories = $connection->fetchPairs(
                $connection->select()
                    ->from($reportTable, ['category_id', 'category_name'])
                    ->distinct(true)
                    ->where('category_name IS NOT NULL')
                    ->where('category_name != ?', '')
                    ->order('category_name ASC')
            );

            $this->logger->info('[CategoryReportService] Loaded ' . count($categories) . ' categories from report');
            return $categories;

        } catch (\Exception $e) {
            $this->logger->error('[CategoryReportService] Error loading categories from report: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Cache category data
     *
     * @param array $categories
     * @return void
     */
    private function cacheCategories(array $categories): void
    {
        try {
            $this->cache->save(
                $this->serializer->serialize($categories),
                self::CACHE_KEY,
                self::CACHE_TAGS,
                self::CACHE_LIFETIME
            );
            $this->logger->debug('[CategoryReportService] Cached ' . count($categories) . ' categories');
        } catch (\Exception $e) {
            $this->logger->error('[CategoryReportService] Failed to cache categories: ' . $e->getMessage());
        }
    }
} 