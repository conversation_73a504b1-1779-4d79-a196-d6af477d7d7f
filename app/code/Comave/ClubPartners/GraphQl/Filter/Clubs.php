<?php

declare(strict_types=1);

namespace Comave\ClubPartners\GraphQl\Filter;

use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\Api\Filter;
use Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor\CustomFilterInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;

class Clubs implements CustomFilterInterface
{
    /**
     * @param Filter $filter
     * @param AbstractDb $collection
     * @return bool
     */
    public function apply(Filter $filter, AbstractDb $collection): bool
    {
        if (!$collection instanceof Collection) {
            return false;
        }

        if (empty($filter->getValue())) {
            throw new GraphQlInputException(
                __(
                    'The "%1" filter must have a value and not be empty',
                    (string) __("Product Club")
                )
            );
        }

        $brandIds = $this->fetchBrandsFromSponsors($collection, $filter->getValue());
        $collection->getSelect()
            ->join(
                ['ccp' => $collection->getTable('comave_club_product')],
                'ccp.product_id = e.entity_id',
                []
            )->join(
                ['cc' => $collection->getTable('comave_club')],
                'cc.club_id = ccp.club_id',
                []
            )->where(
                $filter->getConditionType() === 'in' ?
                    'cc.uniqueid IN (?)' :
                    'cc.uniqueid = ?',
                $filter->getValue()
            );

        if (!empty($brandIds)) {
            $collection->addAttributeToFilter(
                'brand',
                ['in' => $brandIds],
            );
        }

        return true;
    }

    /**
     * @param Collection $collection
     * @param string $clubUniqueId
     * @return array
     */
    private function fetchBrandsFromSponsors(
        Collection $collection,
        string $clubUniqueId
    ): array {
        $connection = $collection->getConnection();
        $brandSelect = $connection->select()
            ->from(
                ['main' => $collection->getTable('comave_club')],
                []
            )->join(
                ['cs' => $collection->getTable('comave_club_sponsors')],
                'cs.club_id = main.club_id',
                []
            )->join(
                ['csb' => $collection->getTable('comave_sponsor_brands')],
                'csb.sponsor_id = cs.sponsor_id',
                [
                    'brand_id'
                ]
            )
            ->where(
                'main.uniqueid = ?',
                $clubUniqueId
            );

        return $connection->fetchCol($brandSelect) ?: [];
    }
}
