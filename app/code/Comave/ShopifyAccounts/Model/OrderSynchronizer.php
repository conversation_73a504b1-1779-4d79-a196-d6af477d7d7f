<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model;

use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\OrderSynchroniseInterface;
use Comave\SellerApi\Service\RequestHandler;
use Comave\ShopifyAccounts\Api\ShopifyApiInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\AlreadyExistsException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\SalesRule\Api\RuleRepositoryInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\OrdersRepository;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\OrdermapInterfaceFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap\Collection;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap\CollectionFactory;

class OrderSynchronizer implements OrderSynchroniseInterface
{
    /**
     * @param OrdersRepository $mpOrderRepository
     * @param RuleRepositoryInterface $ruleRepository
     * @param OrderRepositoryInterface $orderRepository
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param OrdermapInterfaceFactory $shopifyOrderFactory
     * @param Ordermap $resourceModel
     * @param SerializerInterface $serializer
     * @param CollectionFactory $collectionFactory
     * @param RequestHandler $requestHandler
     */
    public function __construct(
        private readonly OrdersRepository $mpOrderRepository,
        private readonly RuleRepositoryInterface $ruleRepository,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly OrdermapInterfaceFactory $shopifyOrderFactory,
        private readonly Ordermap $resourceModel,
        private readonly SerializerInterface $serializer,
        private readonly CollectionFactory $collectionFactory,
        private readonly RequestHandler $requestHandler
    ) {
    }

    /**
     * @param ConfigurableApiInterface $configurableApi
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return void
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function synchroniseOrder(ConfigurableApiInterface $configurableApi, OrderInterface $order, array $orderItems): void
    {
        if (!$configurableApi instanceof ShopifyApiInterface) {
            throw new LocalizedException(
                __(
                    'Shopify Configurable API incorrect, expected %s, provided %s.',
                    ShopifyApiInterface::class,
                    $configurableApi::class
                )
            );
        }

        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter(
            'mage_order_id',
            $order->getEntityId()
        )->addFieldToFilter(
            'rule_id',
            $configurableApi->getShopifyAccount()->getId()
        );

        if ($collection->getSize()) {
            throw new AlreadyExistsException(
                __(
                    'Order already synchronized order_id %1, seller ID %2',
                    $order->getEntityId(),
                    $configurableApi->getShopifyAccount()->getSellerId()
                )
            );
        }

        $orderData = $this->prepareOrderData($order, $orderItems);
        $configurableApi->setParams(json_encode($orderData));
        $response = $this->requestHandler->handleRequest($configurableApi);

        if ($response->hasError()) {
            throw new LocalizedException(
                __($response->getResult()->getBody()->getContents())
            );
        }

        $decodedResponse = $this->serializer->unserialize(
            $response->getResult()->getBody()->getContents() ?? '{}'
        );
        $this->logger->info(
            '[ShopifyOrderSync] Order response magento order',
            [
                'response' => $decodedResponse,
                'order' => $order->getIncrementId()
            ]
        );

        $shopifyOrder = $this->shopifyOrderFactory->create();
        $shopifyOrder->setMageOrderId($order->getEntityId())
            ->setStatus(1)
            ->setShopifyOrderId($decodedResponse['draft_order']['id'])
            ->setCreatedAt(time())
            ->setRuleId($configurableApi->getShopifyAccount()->getId());
        $this->resourceModel->save($shopifyOrder);

        $order->addCommentToStatusHistory(
            sprintf(
                'Successfully created draft order, Seller %s, Shopify Order ID %s',
                $configurableApi->getShopifyAccount()->getSellerId(),
                $decodedResponse['draft_order']['id']
            ),
        );
        $this->orderRepository->save($order);
        $this->logger->info(
            '[ShopifyOrderSync] Successfully synced magento order',
            [
                'shopifyAccount' => $configurableApi->getShopifyAccount()->getShopifyUserId(),
                'sellerId' => $configurableApi->getShopifyAccount()->getSellerId(),
                'orderIncrement' => $order->getIncrementId(),
                'shopifyOrder' => $decodedResponse['draft_order']['id']
            ]
        );
    }

    /**
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return array
     */
    private function prepareOrderData(OrderInterface $order, array $orderItems): array
    {
        $lineItems = [];
        $totalPrice = 0;
        $totalTax = 0;

        foreach ($orderItems as $orderItem) {
            if ($orderItem->getParentItemId() && isset($lineItems[$orderItem->getParentItemId()])) {
                $parentProductId = $lineItems[$orderItem->getParentItemId()]['product_id'];
                unset($lineItems[$orderItem->getParentItemId()]['product_id']);

                if ($variantId = $this->extractVariantId($parentProductId, $orderItem->getSku())) {
                    $lineItems[$orderItem->getParentItemId()]['variant_id'] = $variantId;
                    $lineItems[$orderItem->getParentItemId()]['price'] += $orderItem->getPrice();
                    $lineItems[$orderItem->getParentItemId()]['title'] = $orderItem->getName();
                    $lineItems[$orderItem->getParentItemId()]['tax_lines'] = [
                        [
                            'price' => $orderItem->getTaxAmount(),
                            'rate' => $orderItem->getTaxPercent() / 100,
                            'title' => 'Tax'
                        ]
                    ];
                }
            } else {
                $lineItems[$orderItem->getItemId()] = [
                    'product_id' => $orderItem->getProductId(),
                    'title' => $orderItem->getName(),
                    'requires_shipping' => $orderItem->getWeight() > 0,
                    'taxable' => $orderItem->getTaxAmount() > 0,
                    'price' => $orderItem->getPrice(),
                    'quantity' => (int) $orderItem->getQtyOrdered(),
                    'tax_lines' => [
                        [
                            'price' => $orderItem->getTaxAmount(),
                            'rate' => $orderItem->getTaxPercent() / 100,
                            'title' => 'Tax'
                        ]
                    ]
                ];

                if (!empty($orderItem->getAppliedRuleIds())) {
                    $this->processDiscountRules(
                        $order,
                        $orderItem,
                        $lineItems[$orderItem->getItemId()]
                    );
                }
            }

            $totalPrice += $orderItem->getPrice();
            $totalTax += $orderItem->getTaxAmount();
        }

        $transactions = [
            [
                'kind' => 'sale',
                'status' => 'success',
                'amount' => $totalPrice
            ]
        ];

        $billingAddress = $order->getBillingAddress();
        $shippingAddress = $order->getShippingAddress();
        /** @var CustomerInterface $customer */
        $customer = $order->getCustomer() ?: false;
        $sellerOrder = $this->mpOrderRepository->getByOrderId($order->getId());
        $shippingAmount = $order->getShippingAmount();
        $shippingTaxAmount = $order->getShippingTaxAmount();
        $rate = $order->getShippingTaxAmount() > 0 && $order->getShippingAmount() ?
            number_format($order->getShippingTaxAmount() * 100 / $order->getShippingAmount(), 2) :
            0;
        $shippingDescription = $order->getShippingDescription();

        if ($sellerOrder->getSize()) {
            $shippingAmount = $sellerOrder->getFirstItem()->getShippingCharges();
            $shippingDescription = $sellerOrder->getFirstItem()->getCarrierName();

            if ($shippingAmount > 0) {
                if ($rate > 0) {
                    $shippingTaxAmount = $shippingAmount * $rate / 100;
                }
            } else {
                $shippingTaxAmount = 0;
            }
        }

        $shippingTaxLine = $shippingTaxAmount > 0 ?
            [
                [
                    "price" => number_format(
                        (float) $shippingTaxAmount,
                        2
                    ),
                    "rate" => $rate,
                    "title" => "Shipping Tax"
                ]
            ]: [];
        $shippingAmountFormatted = number_format((float) $shippingAmount, 2);
        $draftOrder = [
            'line_items' => array_values($lineItems),
            'transactions' => $transactions,
            'total_tax' => $totalTax,
            'taxes_included' => false,
            'tax_exempt' => false,
            'currency' => $order->getOrderCurrencyCode(),
            'customer' => [
                'first_name' => $customer ? $customer->getFirstname() : $billingAddress->getFirstname(),
                'last_name' =>  $customer ? $customer->getLastname() : $billingAddress->getLastname(),
                'phone' => (
                    stripos($billingAddress->getTelephone(), '+') === false ? '+' : ''
                    ) . $billingAddress->getTelephone(),
                'email' =>  $customer ? $customer->getEmail() : $billingAddress->getEmail(),
            ],
            'shipping_line' => [
                "code" => str_replace(
                    ' ',
                    '',
                    sprintf(
                        '%s-%s-%s',
                        trim($order->getShippingMethod()),
                        trim($shippingDescription),
                        $shippingAmountFormatted
                    )
                ),
                'custom' => true,
                "price" => $shippingAmountFormatted,
                "title" => $shippingDescription
            ],
            "tax_lines" => $shippingTaxLine,
            'shipping_address' => [
                'first_name' => $shippingAddress->getFirstname(),
                'last_name' => $shippingAddress->getLastname(),
                'address1' => $shippingAddress->getStreetLine(1),
                'address2' => $shippingAddress->getStreetLine(2),
                'city' => $shippingAddress->getCity(),
                'province' => $shippingAddress->getRegion(),
                'country' => $shippingAddress->getCountryId(),
                'zip' => $shippingAddress->getPostcode(),
                'phone' => $shippingAddress->getTelephone(),
            ],
            'billing_address' => [
                'first_name' => $billingAddress->getFirstname(),
                'last_name' => $billingAddress->getLastname(),
                'address1' => $billingAddress->getStreetLine(1),
                'address2' => $billingAddress->getStreetLine(2),
                'city' => $billingAddress->getCity(),
                'province' => $billingAddress->getRegion(),
                'country' => $billingAddress->getCountryId(),
                'zip' => $billingAddress->getPostcode(),
                'phone' => $billingAddress->getTelephone(),
            ],
            'tags' => implode(', ', ['ComAve']), // Combine tags into a comma-separated string
            'note_attributes' => [
                [
                    'name' => 'Magento Order ID',
                    'value' => $order->getIncrementId()
                ]
            ]
        ];

        if (!empty($order->getAppliedRuleIds())) {
            $this->processOrderDiscountRules($order, $draftOrder);
        }

        return [
            'draft_order' => $draftOrder
        ];
    }

    /**
     * @param string $productId
     * @param string $childSku
     * @return string|null
     */
    private function extractVariantId(string $productId, string $childSku): ?string
    {
        $connection = $this->resourceConnection->getConnection();
        $variantIdSelect = $connection->select()
            ->from(
                $connection->getTableName('wk_mpmultishopifysynchronize_product'),
                [
                    'variant_id' => new \Zend_Db_Expr(
                        sprintf("JSON_EXTRACT(`shopify_variant_map`, CONCAT('$.', '%s'))", (string) $childSku)
                    )
                ]
            )->where(
                'magento_pro_id = ?',
                $productId
            );

        return $connection->fetchOne($variantIdSelect) ?: null;
    }

    /**
     * @param OrderInterface $order
     * @param OrderItemInterface $orderItem
     * @param array $orderLineItem
     * @return void
     */
    private function processDiscountRules(
        OrderInterface $order,
        OrderItemInterface $orderItem,
        array &$orderLineItem
    ): void {
        $appliedRule = explode(',', $orderItem->getAppliedRuleIds());

        try {
            $rule = $this->ruleRepository->getById(current($appliedRule));

            if ($rule->getSimpleAction() === \Magento\SalesRule\Model\Rule::CART_FIXED_ACTION) {
                return;
            }

            $ruleLabels = $rule->getStoreLabels();
            $title = $rule->getName();

            if (!empty($ruleLabels)) {
                foreach ($ruleLabels as $label) {
                    if ($label->getStoreId() !== $order->getStoreId()) {
                        continue;
                    }

                    $title = $label->getStoreLabel();
                    break;
                }
            }

            $orderLineItem['applied_discount'] = [
                'title' => $title,
                'description' => $order->getCouponCode() ?: $order->getDiscountDescription() ?: $rule->getName(),
                'value' => (float) ($orderItem->getDiscountAmount() / $orderItem->getQtyOrdered()),
                'value_type' => $this->getTypeBySimpleAction($rule->getSimpleAction()),
                'amount' => (float) ($orderItem->getDiscountAmount() / $orderItem->getQtyOrdered())
            ];
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ShopifyOrderSync] Unable to retrieve rule',
                [
                    'message' => $e->getMessage(),
                    'order' => $order->getEntityId(),
                    'item' => $orderItem->getItemId(),
                    'orderItemRule' => $orderItem->getAppliedRuleIds()
                ]
            );

            return;
        }
    }

    /**
     * @param OrderInterface $order
     * @param array $draftOrder
     * @return void
     */
    private function processOrderDiscountRules(
        OrderInterface $order,
        array &$draftOrder
    ): void {
        $appliedRule = explode(',', $order->getAppliedRuleIds() ?? '');

        try {
            $rule = $this->ruleRepository->getById(current($appliedRule));

            if ($rule->getSimpleAction() !== \Magento\SalesRule\Model\Rule::CART_FIXED_ACTION) {
                return;
            }

            $ruleLabels = $rule->getStoreLabels();
            $title = $rule->getName();

            if (!empty($ruleLabels)) {
                foreach ($ruleLabels as $label) {
                    if ($label->getStoreId() !== $order->getStoreId()) {
                        continue;
                    }

                    $title = $label->getStoreLabel();
                    break;
                }
            }

            $draftOrder['applied_discount'] = [
                'title' => $title,
                'description' => $order->getCouponCode() ?: $order->getDiscountDescription() ?: $rule->getName(),
                'value' => $rule->getDiscountAmount(),
                'value_type' => $this->getTypeBySimpleAction($rule->getSimpleAction()),
                'amount' => (float) $order->getDiscountAmount()
            ];
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ShopifyOrderSync] Unable to retrieve rule',
                [
                    'message' => $e->getMessage(),
                    'order' => $order->getEntityId(),
                    'orderRule' => $order->getAppliedRuleIds()
                ]
            );

            return;
        }
    }

    /**
     * @param string $action
     * @return string
     */
    private function getTypeBySimpleAction(string $action): string
    {
        //Seeing as magento has already calculated the discounts, we always send the final amount
        return 'fixed_amount';

//        return match($action) {
//            \Magento\SalesRule\Model\Rule::BY_FIXED_ACTION, \Magento\SalesRule\Model\Rule::CART_FIXED_ACTION => 'fixed_amount',
//            \Magento\SalesRule\Model\Rule::BY_PERCENT_ACTION => 'percentage',
//            default => $action
//        };
    }
}
