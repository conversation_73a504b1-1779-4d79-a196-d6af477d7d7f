<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model;

use Comave\SellerApi\Service\RequestHandler;
use Comave\SellerOnboarding\Api\CategoryProcessorInterface;
use Comave\ShopifyAccounts\Model\Command\CategoryMapper;
use Comave\ShopifyAccounts\Model\Command\GetCategoryProductsGraphqlString;
use Magento\Catalog\Api\CategoryLinkManagementInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterfaceFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifyaccounts;

class CategoryMappingProcessor implements CategoryProcessorInterface
{
    /**
     * @param LoggerInterface $logger
     * @param RequestHandler $requestHandler
     * @param GetCategoryProductsGraphqlString $getCategoryProductsGraphqlString
     * @param ShopifyaccountsInterfaceFactory $shopifyAccountFactory
     * @param Shopifyaccounts $shopifyaccountsResource
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param CategoryLinkManagementInterface $categoryLinkManagement
     * @param CategoryMapper $categoryMapper
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly GetCategoryProductsGraphqlString $getCategoryProductsGraphqlString,
        private readonly ShopifyaccountsInterfaceFactory $shopifyAccountFactory,
        private readonly Shopifyaccounts $shopifyaccountsResource,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly CategoryLinkManagementInterface $categoryLinkManagement,
        private readonly CategoryMapper $categoryMapper,
    ) {
    }

    /**
     * @param string $sellerId
     * @return void
     * @throws LocalizedException
     * @throws \Psr\Http\Client\ClientExceptionInterface
     */
    public function processCategories(string $sellerId): void
    {
        $shopifyAccount = $this->shopifyAccountFactory->create();
        $this->shopifyaccountsResource->load(
            $shopifyAccount,
            $sellerId,
            'seller_id'
        );

        if (!$shopifyAccount->getId()) {
            throw new LocalizedException(__('Unable to identify shopify account by seller %1', $sellerId));
        }

        $this->logger->info(
            '[ShopifyCategoryMappingProcessor] Beginning to map categories',
            [
                'seller_id' => $sellerId,
                'account_id' => $shopifyAccount->getId(),
            ]
        );

        $hasNextPage = true;
        $products = [];
        $after = null;

        while ($hasNextPage) {
            $queryJson = json_encode([
                'query' => trim($this->getCategoryProductsGraphqlString->get($after))
            ]);
            $configurableApi = $this->configurableApiFactory->create([
                'shopifyAccountId' => $shopifyAccount->getId(),
            ])->setEndpoint(ConfigurableApi::GRAPHQL)
            ->setParams($queryJson)
            ->setMethod('POST');

            try {
                $response = $this->requestHandler->handleRequest($configurableApi);

                if ($response->hasError()) {
                    throw new \Exception($response->getResult()->getReasonPhrase());
                }

                $responseResult = $response->getResult();
                $decodedResponse = json_decode(
                    $responseResult->getBody()->getContents(),
                    true
                );
                $products = array_merge(
                    $products,
                    $decodedResponse['data']['products']['edges'] ?? []
                );

                if (empty($products)) {
                    throw new LocalizedException(__('Unable to process product list'));
                }

                $hasNextPage = $decodedResponse['data']['products']['pageInfo']['hasNextPage'] ?? false;
                $after = $hasNextPage ? (
                    $decodedResponse['data']['products']['pageInfo']['endCursor'] ?? null
                ) : null;
            } catch (LocalizedException $e) {
                $this->logger->error(
                    '[ShopifyCategoryMappingProcessor] Error trying map categories',
                    [
                        'seller_id' => $sellerId,
                        'account_id' => $shopifyAccount->getId(),
                        'error' => $e->getMessage(),
                    ]
                );

                break;
            }
        }

        if (empty($products)) {
            return;
        }

        $this->logger->info(
            '[ShopifyCategoryMappingProcessor] Total products found to map',
            [
                'seller_id' => $sellerId,
                'account_id' => $shopifyAccount->getId(),
                'productCount' => count($products)
            ]
        );

        $categoryData = [];

        foreach ($products as $product) {
            $productData = $product['node'];

            if (empty($productData['productType']) && empty($productData['category'])) {
                $this->logger->warning(
                    '[ShopifyCategoryMappingProcessor] Unable to process product, no categories found in graphql',
                    [
                        'seller_id' => $sellerId,
                        'account_id' => $shopifyAccount->getId(),
                        'product' => $productData,
                    ]
                );

                continue;
            }

            $productId = (string) preg_replace("/[^0-9]/", '', (string) $productData['id']);
            $productSku = strlen($productData['handle']) > 64 ?
                sprintf('%s-%s', $productId, $shopifyAccount->getId()) :
                $productData['handle'];

            $this->logger->info(
                '[ShopifyCategoryMappingProcessor] Processing graphql query to map',
                [
                    'seller_id' => $sellerId,
                    'account_id' => $shopifyAccount->getId(),
                    'sku' => $productSku,
                    'productData' => $productData
                ]
            );

            $categoryData[$productSku] = [];

            if (!empty($productData['productType'])) {
                $categoryData[$productSku][] = $productData['productType'];
            }

            if (!empty($productData['category'])) {
                $categoryData[$productSku][] = $productData['category']['name'];
            }
        }

        $this->logger->info(
            '[ShopifyCategoryMappingProcessor] Final category data to map',
            [
                'seller_id' => $sellerId,
                'account_id' => $shopifyAccount->getId(),
                'products' => $products,
                'categoryData' => $categoryData,
            ]
        );

        foreach ($categoryData as $productSku => $categoryNames) {
            try {
                $magentoCategories = [];

                foreach ($categoryNames as $categoryName) {
                    $magentoCategories = array_merge_recursive(
                        $magentoCategories,
                        $this->categoryMapper->getByShopifyName(
                            $productSku,
                            $categoryName,
                            (string) $shopifyAccount->getId(),
                            $sellerId
                        ) ?: []
                    );
                }

                if (!empty($magentoCategories)) {
                    $this->categoryLinkManagement->assignProductToCategories(
                        $productSku,
                        $magentoCategories
                    );

                    $this->logger->info(
                        '[ShopifyCategoryMappingProcessor] Added product categories',
                        [
                            'seller_id' => $sellerId,
                            'account_id' => $shopifyAccount->getId(),
                            'product' => $productSku,
                            'magentoCategories' => $magentoCategories,
                            'importedCategories' => $categoryNames
                        ]
                    );
                }
            } catch (\Exception $e) {
                $this->logger->warning(
                    '[ShopifyCategoryMappingProcessor] Unable to process product',
                    [
                        'seller_id' => $sellerId,
                        'account_id' => $shopifyAccount->getId(),
                        'product' => $productSku,
                        'error' => $e->getMessage(),
                    ]
                );
            }
        }

        $this->logger->info(
            '[ShopifyCategoryMappingProcessor] Total products mapped',
            [
                'seller_id' => $sellerId,
                'account_id' => $shopifyAccount->getId(),
                'productCount' => count($products)
            ]
        );
    }
}
