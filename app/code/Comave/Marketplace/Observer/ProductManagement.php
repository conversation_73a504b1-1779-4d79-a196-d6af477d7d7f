<?php

declare(strict_types=1);

namespace Comave\Marketplace\Observer;

use Comave\Marketplace\ViewModel\Seller;
use Magento\Catalog\Model\Product\Action;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\Product as MpProduct;
use Webkul\Marketplace\Model\ResourceModel\Product;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory;

class ProductManagement implements ObserverInterface
{
    /**
     * @param Action $productAction
     * @param Product $mpResourceModel
     * @param CollectionFactory $mpCollectionFactory
     * @param Seller $sellerView
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Action $productAction,
        private readonly Product $mpResourceModel,
        private readonly CollectionFactory $mpCollectionFactory,
        private readonly Seller $sellerView,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $product = $observer->getProduct() ?: $observer->getEntity();

        if (!$product instanceof \Magento\Catalog\Model\Product) {
            return;
        }

        $hasSellerAssigned = $product->getAssignSeller();

        if (empty($hasSellerAssigned) || !is_array($hasSellerAssigned)) {
            return;
        }

        try {
            $sellerId = current($hasSellerAssigned);
            $isAutoApprove = $this->sellerView->isSellerAutoApprove((int) $sellerId);
            $product->setStatus($isAutoApprove ?
                Status::STATUS_ENABLED :
                Status::STATUS_DISABLED
            );
            $this->productAction->updateAttributes(
                [$product->getId()],
                [
                    'status' => $product->getStatus()
                ],
                $product->getStoreId()
            );

            $collection = $this->mpCollectionFactory->create();
            $collection->addFieldToFilter('seller_id', $sellerId)
                ->addFieldToFilter('mageproduct_id', $product->getEntityId());
            /** @var MpProduct $marketplaceProduct */
            $marketplaceProduct = $collection->getFirstItem();

            if ($marketplaceProduct->getId()) {
                if ($product->getStatus() !== $marketplaceProduct->getStatus()) {
                    $marketplaceProduct->setStatus($product->getStatus());
                    $this->mpResourceModel->save($marketplaceProduct);
                }
            } else {
                if (!empty($product->getRowId())) {
                    $marketplaceProduct->setMageProRowId($product->getRowId());
                }

                $marketplaceProduct->setMageproductId($product->getEntityId())
                    ->setSellerId($sellerId)
                    ->setStatus($product->getStatus())
                    ->setAdminassign(1)
                    ->setIsApproved($isAutoApprove ? 1 : 0);
                $this->mpResourceModel->save($marketplaceProduct);
            }
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ComaveSellerProduct] Unable to save product for seller',
                [
                    'errorMessage' => $e->getMessage(),
                    'productSku' => $product->getSku()
                ]
            );
        }
    }
}
