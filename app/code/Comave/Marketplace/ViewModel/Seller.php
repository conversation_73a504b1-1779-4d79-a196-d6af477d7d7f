<?php

declare(strict_types=1);

namespace Comave\Marketplace\ViewModel;

use Magento\Customer\Controller\RegistryConstants;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Webkul\Marketplace\Model\SellerFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller as ResourceModel;

class Seller implements ArgumentInterface
{
    public const string PRODUCT_AUTO_APPROVE = 'product_auto_approve';

    /**
     * @param Registry $registry
     * @param SellerFactory $sellerFactory
     * @param ResourceModel $resourceModel
     */
    public function __construct(
        private readonly Registry $registry,
        private readonly SellerFactory $sellerFactory,
        private readonly ResourceModel $resourceModel,
    ) {
    }

    /**
     * @param int|null $customerId
     * @return bool
     */
    public function isSellerAutoApprove(?int $customerId = null): bool
    {
        if (!$customerId && !$this->registry->registry(RegistryConstants::CURRENT_CUSTOMER_ID)) {
            return false;
        }

        $customerId ??= $this->registry->registry(RegistryConstants::CURRENT_CUSTOMER_ID);
        $seller = $this->sellerFactory->create();
        $this->resourceModel->load($seller, $customerId, 'seller_id');

        if (!$seller->getId()) {
            return false;
        }

        $approvalInfo = json_decode($seller->getOthersInfo() ?: '{}', true);

        return $approvalInfo[self::PRODUCT_AUTO_APPROVE] ?? false;
    }

    /**
     * @return int
     */
    public function getSellerId(): int
    {
        return (int) $this->registry->registry(RegistryConstants::CURRENT_CUSTOMER_ID);
    }
}
