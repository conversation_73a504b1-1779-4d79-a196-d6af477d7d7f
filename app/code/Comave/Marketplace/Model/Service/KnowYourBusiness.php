<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Marketplace\Model\Service;

use Comave\SellerStatus\Model\ConfigProvider;
use Comave\SellerStatus\Model\Option\StatusProvider;
use Comave\SellerStatus\Model\RoleValidator\Active;
use Comave\SellerStatus\Model\RoleValidator\Closed;
use Comave\SellerStatus\Model\RoleValidator\HolidayMode;
use Comave\SellerStatus\Model\RoleValidator\Rejected;
use Comave\SellerStatus\Service\Role;
use Magento\Framework\Exception\LocalizedException;
use Throwable;
use Webkul\Marketplace\Model\ResourceModel\Seller;

class KnowYourBusiness
{
    public function __construct(
        private readonly Seller $resourceModel,
        private readonly ConfigProvider $configProvider,
        private readonly StatusProvider $sellerStatusProvider,
        private readonly Role $sellerRoleService,
    ) {
    }

    /**
     * @param int $sellerId
     * @param int|null $roleId
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function updateSellerStatusByRole(int $sellerId, ?int $roleId): void
    {
        if ($this->configProvider->isTestModeEnabled()) {
            return;
        }

        if (!empty($roleId)) {
            $findRoleName = function (array $sellerStatusList) use ($roleId) {
                foreach ($sellerStatusList as $sellerStatus) {
                    if ($roleId === (int)$sellerStatus['value']) {
                        return $sellerStatus['label'];
                    }
                }

                return null;
            };
            $sellerStatusList = $this->sellerStatusProvider->toOptionArray();
            $roleName = $findRoleName($sellerStatusList);
            if (empty($roleName)) {
                throw new LocalizedException(__('Unable to determine the role name.'));
            }
        } else {
            $role = $this->sellerRoleService->get($sellerId);
            if (!$role) {
                throw new LocalizedException(__('Unable to determine seller role.'));
            }
            $roleName = $role->getRoleName();
        }

        $this->updateSellerStatus(
            $sellerId,
            match ($roleName) {
                Active::ROLE_NAME, HolidayMode::ROLE_NAME => 'verified',
                Rejected::ROLE_NAME, Closed::ROLE_NAME => 'failed',
                default => 'pending',
            }
        );
    }

    /**
     * @param int $sellerId
     * @param string $status
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function updateSellerStatus(int $sellerId, string $status): void
    {
        $connection = $this->resourceModel->getConnection();
        $connection->beginTransaction();
        try {
            $connection->update(
                $connection->getTableName('marketplace_userdata'),
                [
                    'kyb_status' => $status,
                ],
                [
                    'seller_id = ?' => $sellerId,
                ]
            );
            $connection->commit();
        } catch (Throwable $e) {
            $connection->rollBack();
            throw new LocalizedException(__('Unable to set KYB status to seller, %1', $e->getMessage()));
        }
    }
}