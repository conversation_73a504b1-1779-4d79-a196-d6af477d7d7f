<?php

declare(strict_types=1);

namespace Comave\Marketplace\Model\Queue\Consumer;

use Comave\Marketplace\Api\ApprovalFlowInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Area;
use Magento\Framework\DB\Select;
use Magento\Framework\Event\ManagerInterface;
use Magento\Store\Model\App\Emulation;
use Webkul\Marketplace\Helper\Data;
use Webkul\Marketplace\Helper\Email;
use Webkul\Marketplace\Helper\Notification;
use Webkul\Marketplace\Model\Notification as NotificationModel;
use Webkul\Marketplace\Model\ProductFactory;
use Webkul\Marketplace\Model\ResourceModel\Product;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class ApprovalFlow
{
    public const string TOPIC_NAME = 'seller.product.approvalFlow';

    /**
     * @param Emulation $emulation
     * @param ProductFactory $productFactory
     * @param Product $resourceModel
     * @param Data $mpHelper
     * @param ManagerInterface $eventManager
     * @param ProductRepositoryInterface $productRepository
     * @param Seller $sellerResourceModel
     * @param CustomerRepositoryInterface $customerRepository
     * @param SellerFactory $sellerFactory
     * @param CollectionFactory $collectionFactory
     * @param Email $mpEmailHelper
     * @param Notification $mpNotificationHelper
     */
    public function __construct(
        private readonly Emulation $emulation,
        private readonly ProductFactory $productFactory,
        private readonly Product $resourceModel,
        private readonly Data $mpHelper,
        private readonly ManagerInterface $eventManager,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly Seller $sellerResourceModel,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly SellerFactory $sellerFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly Email $mpEmailHelper,
        private readonly Notification $mpNotificationHelper,
    ) {
    }

    /**
     * @param ApprovalFlowInterface $approvalFlowMessage
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(ApprovalFlowInterface $approvalFlowMessage): void
    {
        $marketplaceProduct = $this->productFactory->create();
        $this->resourceModel->load(
            $marketplaceProduct,
            $approvalFlowMessage->getProductId(),
            'mageproduct_id'
        );

        if (!$marketplaceProduct->getId()) {
            return;
        }

        /** @var \Webkul\Marketplace\Model\Seller $seller */
        $seller = $this->sellerFactory->create();
        $this->sellerResourceModel->load(
            $seller,
            $marketplaceProduct->getSellerId(),
            'seller_id'
        );

        if (!$seller->getId()) {
            return;
        }

        $sellerEmail = $seller->getEmail();
        $sellerName = $seller->getOwnerName();

        if (empty($sellerEmail) || empty($sellerName)) {
            /** @var CustomerInterface $customer */
            $customer = $this->customerRepository->getById(
                (int) $marketplaceProduct->getSellerId()
            );
            $sellerEmail = $customer->getEmail();
            $sellerName = $customer->getFirstname() . ' ' . $customer->getLastname();
        }

        $this->mpNotificationHelper->saveNotification(
            NotificationModel::TYPE_PRODUCT,
            $marketplaceProduct->getId(),
            $marketplaceProduct->getMageproductId()
        );
        $productModel = $this->productRepository->getById(
            (int) $approvalFlowMessage->getProductId()
        );
        $adminStoreEmail = $this->mpHelper->getAdminEmailId();
        $adminEmail = $adminStoreEmail ?: $this->mpHelper->getDefaultTransEmailId();
        $adminUsername = $this->mpHelper->getAdminName();
        $emailTemplateVariables = [
            'myvar1' => $productModel->getName(),
            'myvar2' => empty($approvalFlowMessage->getReason()) ?
                $productModel->getCustomAttribute('description')?->getValue() :
                $approvalFlowMessage->getReason(),
            'myvar3' => $productModel->getPrice(),
            'myvar4' => $this->buildCategoryPath($productModel),
            'myvar5' => $sellerName,
            'myvar6' => (string) __(
                'We would like to inform you that your product has been %1.',
                $approvalFlowMessage->getIsApproved() ? 'approved' : 'rejected'
            ),
        ];
        $senderInfo = ['name' => $adminUsername, 'email' => $adminEmail];
        $receiverInfo = ['name' => $seller->getName(), 'email' => $sellerEmail];
        $approvalFlowMessage->getIsApproved() ?
            $this->mpEmailHelper->sendProductStatusMail(
                $emailTemplateVariables,
                $senderInfo,
                $receiverInfo
            ) :
            $this->mpEmailHelper->sendProductUnapproveMail(
                $emailTemplateVariables,
                $senderInfo,
                $receiverInfo
            );

        try {
            $this->emulation->startEnvironmentEmulation(
                0,
                Area::AREA_ADMINHTML,
                true
            );
        } catch (\Exception) {
        }

        $this->eventManager->dispatch(
            $approvalFlowMessage->getIsApproved() ?
                'mp_approve_product' :
                'mp_disapprove_product',
            [
                'product' => $marketplaceProduct,
                'seller' => $seller
            ]
        );

        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * @param ProductInterface $productModel
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function buildCategoryPath(ProductInterface $productModel): string
    {
        if (empty($productModel->getCategoryIds())) {
            return 'N/A';
        }

        $collection = $this->collectionFactory->create();
        $collection->getSelect()->reset(Select::COLUMNS);
        $collection->addAttributeToSelect('name', 'inner');
        $collection->addFieldToFilter(
            'entity_id',
            ['in' => $productModel->getCategoryIds()]
        );

        $result = [];

        /** @var Category $category */
        foreach ($collection->getItems() as $category) {
            $result[] = $category->getName();
        }

        return implode(', ', $result);
    }
}
