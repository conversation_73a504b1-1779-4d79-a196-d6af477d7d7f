<?php

declare(strict_types=1);

namespace Comave\Marketplace\Model\Dto;

use Comave\Marketplace\Api\ApprovalFlowInterface;

class ApprovalFlow implements ApprovalFlowInterface
{
    private ?bool $isApproved = null;
    private ?string $reason = null;
    private ?int $productId = null;

    /**
     * @param bool $isApproved
     * @return self
     */
    public function setIsApproved(bool $isApproved): ApprovalFlowInterface
    {
        $this->isApproved = $isApproved;

        return $this;
    }

    /**
     * @param int $productId
     * @return self
     */
    public function setProductId(int $productId): ApprovalFlowInterface
    {
        $this->productId = $productId;

        return $this;
    }

    /**
     * @return int
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * @return bool
     */
    public function getIsApproved(): bool
    {
        return $this->isApproved;
    }

    /**
     * @param string $reason
     * @return self
     */
    public function setReason(string $reason): ApprovalFlowInterface
    {
        $this->reason = $reason;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getReason(): ?string
    {
        return $this->reason;
    }
}
