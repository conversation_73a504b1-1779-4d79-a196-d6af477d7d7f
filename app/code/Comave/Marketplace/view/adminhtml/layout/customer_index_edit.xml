<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_form">
            <block class="Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Product" name="seller_edit_product_tab_view" as="seller-product-tab" >
                <arguments>
                    <argument xsi:type="object" name="sellerViewModel">Comave\Marketplace\ViewModel\Seller</argument>
                    <argument name="config" xsi:type="array">
                        <item name="label" xsi:type="string" translate="true">Product Assignment</item>
                        <item name="collapsible" xsi:type="boolean">true</item>
                        <item name="opened" xsi:type="boolean">true</item>
                        <item name="sortOrder" xsi:type="string">2</item>
                        <item name="canShow" xsi:type="boolean">true</item>
                        <item name="componentType" xsi:type="string">fieldset</item>
                    </argument>
                </arguments>
            </block>
            <block class="Webkul\Marketplace\Block\Adminhtml\Customer\Edit\RemoveSellerTab" name="seller_edit_removeseller_tab_view" />
        <block class="Webkul\Marketplace\Block\Adminhtml\Customer\Edit\AddSellerTab" name="seller_edit_addseller_tab_view" />
    </referenceBlock>
    </body>
</page>
