<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<?php
    /** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer  */
    /** @var \Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Product $block */
    /** @var \Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid\Product $blockGrid */
    /** @var \Comave\Marketplace\ViewModel\Seller $sellerViewModel */
    $sellerViewModel = $block->getData('sellerViewModel');
    $blockGrid = $block->getBlockGrid();
    $gridJsObjectName = $blockGrid->getJsObjectName();
    $value = $sellerViewModel->isSellerAutoApprove();
$scriptString = <<<AUTOAPPROVESCRIPT
require([
    'jquery',
    'Magento_Ui/js/modal/alert'
], function($, alert) {
    'use strict';

    $(document).on('change', '#seller_auto_approval', function () {
        $('body').trigger('processStart');
        $.ajax({
            url: '{$block->getUrl("marketplace/seller_approval/update")}',
            type: 'POST',
            dataType: 'json',
            data: {
                seller_id: '{$sellerViewModel->getSellerId()}',
                isEnabled: parseInt($(this).val())
            },
            success: function (response) {
                $('body').trigger('processStop');
                if (response.success) {
                    alert({
                        title: 'Success',
                        content: response.message || 'Action completed successfully.',
                    });
                } else {
                    alert({
                        title: 'Server Error',
                        content: response.message || 'Something went wrong on the server.',
                        type: 'error'
                    });
                }
            },
            error: function (xhr, status, error) {
                $('body').trigger('processStop');
                alert({
                    title: 'AJAX Error',
                    content: 'Error occurred: ' + error,
                    type: 'error'
                });
            }
        });
    });
});
AUTOAPPROVESCRIPT;

?>
<?= $block->getGridHtml(); ?>
<script type="text/x-magento-init">
    {
        "*": {
            "Webkul_Marketplace/js/seller/assign-products": {
                "selectedProducts": <?= /* @noEscape */ $block->getSellerAssignedProductsJson(); ?>,
                "gridJsObjectName": <?= /* @escapeNotVerified */ '"' . $gridJsObjectName . '"' ?: '{}'; ?>
            }
        }
    }
</script>
<!-- @todo remove when "UI components" will support such initialization -->
<script>
    require('mage/apply/main').apply();
</script>
<input type="hidden" name="sellerassignproid" id="in_adminassign_products" data-form-part="customer_form" value="" />
<div class="fieldset-wrapper">
    <div class="fieldset-wrapper-title">
        <strong class="title"><?= /** @noEscape */__('Auto Approval Settings'); ?></strong>
    </div>
    <div class="admin__fieldset-wrapper-content">
        <fieldset class="admin__fieldset">
            <div class="admin__field field">
                <label class="label admin__field-label" for="seller_auto_approval">
                    <span><?= /** @noEscape  */ __('Enable Product Auto Approval'); ?></span>
                </label>
                <div class="admin__field-control control">
                    <select name="seller_auto_approval" id="seller_auto_approval" class="admin__control-select">
                        <option value="1"<?= ($value === true) ? ' selected' : ''; ?>><?= /** @noEscape  */ __('Yes'); ?></option>
                        <option value="0"<?= ($value !== true) ? ' selected' : ''; ?>><?= /** @noEscape  */ __('No'); ?></option>
                    </select>
                </div>
            </div>
        </fieldset>
    </div>
</div>
<?= /** @noEscape  */ $secureRenderer->renderTag('script', [], $scriptString, false); ?>


