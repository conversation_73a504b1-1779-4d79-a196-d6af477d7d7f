<?php

declare(strict_types=1);

namespace Comave\Marketplace\Controller\Adminhtml\Seller\Approval;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Comave\Marketplace\ViewModel\Seller as SellerViewModel;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class Update extends Action implements HttpPostActionInterface
{
    public const ADMIN_RESOURCE = 'Comave_Marketplace::seller_update_approval';

    /**
     * @param Context $context
     * @param Seller $sellerResource
     * @param SellerFactory $sellerFactory
     */
    public function __construct(
        Context $context,
        private readonly Seller $sellerResource,
        private readonly SellerFactory $sellerFactory,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        $jsonResult = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        if (
            !$this->getRequest()->isAjax()
            || !$this->getRequest()->getParam('seller_id')
        ) {
            return $jsonResult->setData([
                'success' => false,
                'message' => __('Invalid request')
            ]);
        }

        $seller = $this->sellerFactory->create();
        $this->sellerResource->load(
            $seller,
            (int) $this->getRequest()->getParam('seller_id'),
            'seller_id'
        );

        if (!$seller->getId()) {
            return $jsonResult->setData([
                'success' => false,
                'message' => __('Invalid request')
            ]);
        }

        $jsonData = [
            'success' => true,
            'message' => ''
        ];

        try {
            $info = json_decode($seller->getOthersInfo() ?: '{}', true);
            $info[SellerViewModel::PRODUCT_AUTO_APPROVE] = (bool) $this->getRequest()->getParam('isEnabled');
            $seller->setData('others_info', json_encode($info));
            $this->sellerResource->save($seller);
            $jsonData['message'] = __('Product auto approval has been updated successfully.');
        } catch (\Exception $e) {
            $jsonData['success'] = false;
            $jsonData['message'] = $e->getMessage();
        }

        return $jsonResult->setData($jsonData);
    }
}
