<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Adminhtml\Product;

use Comave\Marketplace\Api\ApprovalFlowInterfaceFactory;
use Comave\Marketplace\Model\Queue\Consumer\ApprovalFlow;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Catalog\Model\Product\Action;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Ui\Component\MassAction\Filter;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory;

class MassApprove extends \Magento\Backend\App\Action implements HttpPostActionInterface
{
    public const string ADMIN_RESOURCE = 'Webkul_Marketplace::product';

    /**
     * @param Context $context
     * @param ApprovalFlowInterfaceFactory $approveMessageFactory
     * @param Filter $filter
     * @param StoreManagerInterface $storeManager
     * @param CollectionFactory $collectionFactory
     * @param Action $productAction
     * @param PublisherInterface $publisher
     */
    public function __construct(
        Context $context,
        private readonly ApprovalFlowInterfaceFactory $approveMessageFactory,
        private readonly Filter $filter,
        private readonly StoreManagerInterface $storeManager,
        private readonly CollectionFactory $collectionFactory,
        private readonly Action $productAction,
        private readonly PublisherInterface $publisher
    ) {
        parent::__construct($context);
    }

    /**
     * @return Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Redirect
    {
        try {
            $collection = $this->filter->getCollection($this->collectionFactory->create());
            $productIds = $collection->getAllIds();
            $allStores = $this->storeManager->getStores();
            $sellerProduct = $this->collectionFactory->create();

            $details = [
                'status' => Status::STATUS_ENABLED,
                'seller_pending_notification' => 1,
                'is_approved' => 1
            ];
            $sellerProduct->setProductData(
                [
                    'mageproduct_id IN (?)' => $productIds
                ],
                $details
            );

            foreach ($allStores as $store) {
                $this->productAction->updateAttributes(
                    $productIds,
                    [
                        'status' => Status::STATUS_ENABLED
                    ],
                    $store->getId()
                );
            }

            foreach ($productIds as $productId) {
                $message = $this->approveMessageFactory->create();
                $message->setProductId((int) $productId)
                    ->setIsApproved(true);
                $this->publisher->publish(ApprovalFlow::TOPIC_NAME, $message);
            }

            $this->messageManager->addSuccessMessage(
                __(
                    'A total of %1 record(s) have been approved.',
                    $collection->getSize()
                )
            );
        } catch (\Throwable $e) {
            $this->getMessageManager()->addErrorMessage($e->getMessage());
        }

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setPath('*/*/');
    }
}
