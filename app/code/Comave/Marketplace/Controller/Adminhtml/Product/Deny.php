<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Adminhtml\Product;

use Comave\Marketplace\Api\ApprovalFlowInterfaceFactory;
use Comave\Marketplace\Model\Queue\Consumer\ApprovalFlow;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Catalog\Model\Product\Action;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Store\Model\StoreManagerInterface;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory;

class Deny extends \Magento\Backend\App\Action implements HttpGetActionInterface
{
    public const string ADMIN_RESOURCE = 'Webkul_Marketplace::product';

    /**
     * @param Context $context
     * @param ApprovalFlowInterfaceFactory $approveMessageFactory
     * @param StoreManagerInterface $storeManager
     * @param CollectionFactory $collectionFactory
     * @param Action $productAction
     * @param PublisherInterface $publisher
     */
    public function __construct(
        Context $context,
        private readonly ApprovalFlowInterfaceFactory $approveMessageFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly CollectionFactory $collectionFactory,
        private readonly Action $productAction,
        private readonly PublisherInterface $publisher
    ) {
        parent::__construct($context);
    }

    /**
     * @return Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Redirect
    {
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        if (empty($this->getRequest()->getParam('mageproduct_id'))) {
            $this->messageManager->addErrorMessage(
                __('Invalid request')
            );

            return $resultRedirect->setPath('*/*/');
        }

        $productIds = [$this->getRequest()->getParam('mageproduct_id')];
        $allStores = $this->storeManager->getStores();
        $sellerProduct = $this->collectionFactory->create();

        $details = [
            'status' => \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED,
            'seller_pending_notification' => 1,
            'is_approved' => 0
        ];
        $sellerProduct->setProductData(
            [
                'mageproduct_id IN (?)' => $productIds
            ],
            $details
        );

        foreach ($allStores as $store) {
            $this->productAction->updateAttributes(
                $productIds,
                [
                    'status' => \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_DISABLED
                ],
                $store->getId()
            );
        }

        $sanitiseReason = filter_var(
            $this->getRequest()->getParam('product_deny_reason') ?? '',
            FILTER_UNSAFE_RAW
        );
        foreach ($productIds as $productId) {
            $message = $this->approveMessageFactory->create();
            $message->setProductId((int) $productId)
                ->setIsApproved(false)
                ->setReason(strip_tags($sanitiseReason));
            $this->publisher->publish(ApprovalFlow::TOPIC_NAME, $message);
        }

        $this->messageManager->addSuccessMessage(
            __(
                'The product has been denied successfully.'
            )
        );
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        return $resultRedirect->setPath('*/*/');
    }
}
