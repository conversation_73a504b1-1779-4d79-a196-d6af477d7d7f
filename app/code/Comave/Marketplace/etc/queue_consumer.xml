<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="seller.product.approvalFlow"
              queue="seller.product.approvalFlow"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Marketplace\Model\Queue\Consumer\ApprovalFlow::execute"/>
</config>
