<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="catalog_product_save_after">
        <observer name="webkul_marketplace_catalog_product_save_after_observer" disabled="true"/>
        <observer name="checkSellerProductStatus" instance="Comave\Marketplace\Observer\ProductManagement"/>
    </event>
    <event name="magento_catalog_api_data_productinterface_save_after">
        <observer name="webkul_marketplace_catalog_productapi_save_after_observer" disabled="true"/>
        <observer name="checkSellerProductStatusEntityManager" instance="Comave\Marketplace\Observer\ProductManagement"/>
    </event>
</config>
