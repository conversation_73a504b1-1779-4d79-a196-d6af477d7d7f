<?php

declare(strict_types=1);

namespace Comave\Marketplace\Api;

interface ApprovalFlowInterface
{
    /**
     * @param bool $isApproved
     * @return self
     */
    public function setIsApproved(bool $isApproved): self;

    /**
     * @param int $productId
     * @return self
     */
    public function setProductId(int $productId): self;

    /**
     * @return int
     */
    public function getProductId(): int;

    /**
     * @return bool
     */
    public function getIsApproved(): bool;

    /**
     * @param string $reason
     * @return self
     */
    public function setReason(string $reason): self;

    /**
     * @return string|null
     */
    public function getReason(): ?string;
}
