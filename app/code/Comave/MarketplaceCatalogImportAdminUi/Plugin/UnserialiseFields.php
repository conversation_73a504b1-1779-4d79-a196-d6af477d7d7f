<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImportAdminUi\Plugin;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterfaceFactory;
use Comave\MarketplaceCatalogImport\Model\Command\TemplateDownloader;
use Magento\Framework\Api\AttributeInterfaceFactory;
use Magento\Framework\Api\Search\SearchCriteriaInterface;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\View\Element\UiComponent\DataProvider\Reporting;

class UnserialiseFields
{
    /**
     * @param SerializerInterface $serializer
     * @param AttributeInterfaceFactory $attributeValueFactory
     * @param ExportEntityInterfaceFactory $entityInterfaceFactory
     * @param TemplateDownloader $templateDownloader
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly AttributeInterfaceFactory $attributeValueFactory,
        private readonly ExportEntityInterfaceFactory $entityInterfaceFactory,
        private readonly TemplateDownloader $templateDownloader
    ) {
    }

    /**
     * @param Reporting $reporting
     * @param AbstractCollection $collection
     * @param SearchCriteriaInterface $searchCriteria
     * @return AbstractCollection
     * @throws \Exception
     */
    public function afterSearch(
        Reporting $reporting,
        AbstractCollection $collection,
        SearchCriteriaInterface $searchCriteria
    ): AbstractCollection {
        if ($searchCriteria->getRequestName() !== 'export_entity_form_data_source') {
            return $collection;
        }

        foreach ($collection->getItems() as $item) {
            if (empty($item->getData(ExportEntityInterface::ATTRIBUTE_CONFIGURATION))) {
                continue;
            }

            $item->setData(
                ExportEntityInterface::ATTRIBUTE_CONFIGURATION,
                $this->serializer->unserialize(
                    $item->getData(ExportEntityInterface::ATTRIBUTE_CONFIGURATION)
                )
            );
        }

        if ($collection->getItems()) {
            return $collection;
        }

        /** @var AbstractModel $entity */
        $entity = $this->entityInterfaceFactory->create();
        $dataArr = [
            ExportEntityInterface::ATTRIBUTE_CONFIGURATION =>
                $this->templateDownloader->getMinimumRequiredAttributes(),
            ExportEntityInterface::ID => null,
            ExportEntityInterface::NAME => null,
            ExportEntityInterface::COUNTRY_ID => [],
        ];

        $customAttributes = [];

        foreach ($dataArr as $attributeCode => $value) {
            $customAttributes[] = $this->attributeValueFactory->create([
                'data' => [
                    'attribute_code' => $attributeCode,
                    'value' => $value,
                ]
            ]);
        }

        $entity->setCustomAttributes($customAttributes);
        $collection->addItem($entity);

        return $collection;
    }
}
