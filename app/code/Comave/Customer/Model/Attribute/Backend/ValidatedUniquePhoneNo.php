<?php

declare(strict_types=1);

namespace Comave\Customer\Model\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Comave\Customer\Service\Validator\PhoneNumberValidator;
use Comave\Customer\Exception\InvalidPhoneNumberException;

/**
 * Backend model to validate format and enforce uniqueness of the 'phone_no' attribute.
 * 
 * This class handles both phone number format validation using PhoneNumberValidator
 * and ensures uniqueness across all customer records in the database.
 */
class ValidatedUniquePhoneNo extends AbstractBackend
{
    /**
     * Constructor for ValidatedUniquePhoneNo.
     *
     * @param CustomerRepositoryInterface $customerRepository Repository to access customer data
     * @param SearchCriteriaBuilder $criteriaBuilder Builder for constructing search criteria
     * @param PhoneNumberValidator $phoneNumberValidator Service for validating phone number format
     */
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly SearchCriteriaBuilder $criteriaBuilder,
        private readonly PhoneNumberValidator $phoneNumberValidator
    ) {
    }

    /**
     * Executes before the customer entity is saved.
     *
     * Validates phone number format and ensures uniqueness among all customers.
     *
     * @param CustomerInterface|\Magento\Framework\DataObject $customer
     * @return AbstractBackend
     * @throws LocalizedException If the phone number is invalid or already used by another customer
     */
    public function beforeSave($customer): AbstractBackend
    {
        $attributeCode = $this->getAttribute()->getAttributeCode();
        $phone = $customer->getData($attributeCode);

        if (!$phone) {
            return parent::beforeSave($customer);
        }

        // Validate and normalize phone number format
        try {
            $phone = $this->phoneNumberValidator->validate($phone);
            $customer->setData($attributeCode, $phone);
        } catch (InvalidPhoneNumberException $e) {
            throw new LocalizedException(
                __("Invalid phone number format. Please enter a valid phone number.")
            );
        }

        // Check for uniqueness
        $criteria = $this->criteriaBuilder
            ->addFilter($attributeCode, $phone)
            ->addFilter('entity_id', $customer->getId() ?: 0, 'neq')
            ->create();

        $existingCustomers = $this->customerRepository->getList($criteria);

        if ($existingCustomers->getTotalCount() > 0) {
            throw new LocalizedException(
                __("A customer with the phone number '%1' already exists.", $phone)
            );
        }

        return parent::beforeSave($customer);
    }
}
