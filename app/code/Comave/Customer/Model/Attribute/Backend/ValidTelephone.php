<?php

declare(strict_types=1);

namespace Comave\Customer\Model\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\Exception\LocalizedException;
use Comave\Customer\Service\Validator\PhoneNumberValidator;

/**
 * Backend model to validate the format of telephone attributes for customer entities.
 * 
 * This class provides phone number format validation and normalization using
 * the PhoneNumberValidator service. It can be applied to any telephone-type attribute.
 */
class ValidTelephone extends AbstractBackend
{
    /**
     * Constructor for ValidTelephone.
     *
     * @param PhoneNumberValidator $phoneNumberValidator Service for validating phone number format
     */
    public function __construct(
        private readonly PhoneNumberValidator $phoneNumberValidator
    ) {}

    /**
     * Validates the phone number format before saving the entity.
     *
     * @param \Magento\Framework\DataObject $object The object being saved
     * @return AbstractBackend
     * @throws LocalizedException If the phone number format is invalid
     */
    public function beforeSave($object): AbstractBackend
    {
        $attributeCode = $this->getAttribute()->getAttributeCode();
        $phone = $object->getData($attributeCode);

        if (!$phone) {
            return parent::beforeSave($object);
        }

        try {
            $validated = $this->phoneNumberValidator->validate($phone);
            $object->setData($attributeCode, $validated);
        } catch (LocalizedException $e) {
            throw new LocalizedException(
                __("Invalid phone number: %1", $e->getMessage())
            );
        }

        return parent::beforeSave($object);
    }
}
