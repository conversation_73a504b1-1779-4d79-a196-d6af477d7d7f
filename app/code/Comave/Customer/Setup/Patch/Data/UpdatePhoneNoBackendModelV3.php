<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Customer\Model\Customer;
use Comave\Customer\Model\Attribute\Backend\ValidatedUniquePhoneNo;

/**
 * Data patch that updates the backend model for phone_no attribute to ValidatedUniquePhoneNo.
 * 
 * This patch migrates from the previous UniquePhoneNo backend model to the new
 * ValidatedUniquePhoneNo model which provides both validation and uniqueness checking.
 */
class UpdatePhoneNoBackendModelV3 implements DataPatchInterface
{
    /**
     * Constructor for UpdatePhoneNoBackendModelV3.
     *
     * @param ModuleDataSetupInterface $moduleDataSetup Setup interface for DB operations
     * @param CustomerSetupFactory $customerSetupFactory Factory to create CustomerSetup instances
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly CustomerSetupFactory $customerSetupFactory
    ) {}

    /**
     * Apply the patch.
     *
     * Updates the backend model for phone_no attribute to use ValidatedUniquePhoneNo.
     *
     * @return $this
     * @throws \RuntimeException If the attribute does not already exist
     */
    public function apply(): self
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $customerSetup = $this->customerSetupFactory->create(
            ['setup' => $this->moduleDataSetup]
        );

        $attribute = $customerSetup->getAttribute(Customer::ENTITY, 'phone_no');
        if (!$attribute) {
            throw new \RuntimeException("'phone_no' attribute does not exist");
        }

        $customerSetup->updateAttribute(
            Customer::ENTITY,
            'phone_no',
            [
                'backend_model' => ValidatedUniquePhoneNo::class
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    /**
     * Get array of patches that this patch depends on.
     *
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [
            \Comave\Customer\Setup\Patch\Data\UniquePhoneNoAttributeV2::class
        ];
    }

    /**
     * Get aliases (previous names) for this patch.
     *
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }
}
