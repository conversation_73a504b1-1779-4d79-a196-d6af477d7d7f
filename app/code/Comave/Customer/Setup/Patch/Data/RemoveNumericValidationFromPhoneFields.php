<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Customer\Model\Customer;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Serialize\SerializerInterface;

class RemoveNumericValidationFromPhoneFields implements DataPatchInterface
{
    /**
     * Constructor for RemoveNumericValidationFromPhoneFields.
     *
     * @param ModuleDataSetupInterface $moduleDataSetup Setup interface for module data operations
     * @param EavSetupFactory $eavSetupFactory Factory to create EAV setup instances
     * @param SerializerInterface $serializer Serializer for encoding validation rules
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly SerializerInterface $serializer
    ) {}

    public function apply(): void
    {
        /** @var EavSetup $setup */
        $setup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $this->moduleDataSetup->startSetup();

        $rules = [
            'min_text_length' => '1',
            'max_text_length' => '255'
        ];

        $setup->updateAttribute(
            Customer::ENTITY,
            'phone_no',
            'validate_rules',
            $this->serializer->serialize($rules)
        );

        $setup->updateAttribute(
            'customer_address',
            'telephone',
            'validate_rules',
            $this->serializer->serialize($rules)
        );

        $setup->updateAttribute(
            'customer_address',
            'telephone',
            'backend_model',
            \Comave\Customer\Model\Attribute\Backend\ValidTelephone::class
        );

        $this->moduleDataSetup->endSetup();
    }

    public static function getDependencies(): array
    {
        return [\Comave\Customer\Setup\Patch\Data\CustomerSetupV3::class];
    }

    public function getAliases(): array
    {
        return [];
    }
}
