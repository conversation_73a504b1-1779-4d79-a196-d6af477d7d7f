<?php
declare(strict_types=1);

namespace Comave\Customer\Exception;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Phrase;

/**
 * Exception thrown when a phone number is invalid or cannot be parsed.
 */
class InvalidPhoneNumberException extends LocalizedException
{
    /**
     * Constructor for InvalidPhoneNumberException.
     *
     * @param Phrase|string|null $message The exception message
     * @param \Throwable|null $cause The previous throwable used for exception chaining
     */
    public function __construct(Phrase|string|null $message = null, ?\Throwable $cause = null)
    {
        parent::__construct(
            $message instanceof Phrase ? $message : __($message),
            $cause
        );
    }
}
