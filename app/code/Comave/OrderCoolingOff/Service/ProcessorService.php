<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Service;

use Magento\Sales\Model\Order;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Comave\Sales\Service\Order\CancellationService;
use Psr\Log\LoggerInterface;

/**
 * Processor service for cooling-off period operations
 */
class ProcessorService
{
    /**
     * @param EventManagerInterface $eventManager
     * @param CancellationService $cancellationService
     * @param ValidationService $validationService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly EventManagerInterface $eventManager,
        private readonly CancellationService $cancellationService,
        private readonly ValidationService $validationService,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Transition order from cooling-off to accepted status
     *
     * @param Order $order
     * @return void
     */
    public function transitionToAccepted(Order $order): void
    {
        $this->validationService->allowTransition();

        try {
            $intendedStatus = $order->getData('intended_status_after_cooling_off');
            $intendedState = $order->getData('intended_state_after_cooling_off');

            // Fallback to default values if intended values are null
            if ($intendedStatus === null) {
                $intendedStatus = 'processing';
                $this->logger->warning(
                    'Intended status after cooling-off was null, using fallback',
                    [
                        'order_id' => $order->getEntityId(),
                        'fallback_status' => $intendedStatus
                    ]
                );
            }

            if ($intendedState === null) {
                $intendedState = Order::STATE_PROCESSING;
                $this->logger->warning(
                    'Intended state after cooling-off was null, using fallback',
                    [
                        'order_id' => $order->getEntityId(),
                        'fallback_state' => $intendedState
                    ]
                );
            }

            $order->setStatus($intendedStatus)
                  ->setState($intendedState);

            $order->addCommentToStatusHistory(
                __('Cooling-off period expired. Order moved to %1.', $intendedStatus),
                $intendedStatus,
                false
            );

            $order->unsetData('intended_status_after_cooling_off');
            $order->unsetData('intended_state_after_cooling_off');

            $this->eventManager->dispatch(
                'comave_order_cooling_off_expired',
                [
                    'order' => $order,
                    'previous_status' => 'cooling_off',
                    'new_status' => $intendedStatus
                ]
            );

        } finally {
            $this->validationService->disallowTransition();
        }
    }

    /**
     * Cancel order during cooling-off period
     *
     * @param Order $order
     * @param string $reason
     * @return void
     */
    public function cancelDuringCoolingOff(Order $order, string $reason = ''): void
    {


        $this->validationService->allowTransition();

        try {
            $comment = $reason
                ? __('Order cancelled by customer during cooling-off period. Reason: %1', $reason)
                : __('Order cancelled by customer during cooling-off period.');

            $this->cancellationService->cancelOrderWithReason(
                $order,
                'canceled_by_customer',
                (string) $comment
            );

            $this->eventManager->dispatch(
                'comave_order_cooling_off_cancelled',
                [
                    'order' => $order,
                    'reason' => $reason
                ]
            );


        } finally {
            $this->validationService->disallowTransition();
        }
    }
}
