<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Service;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Configuration provider for cooling-off period functionality
 */
class ConfigProvider
{
    private const string XML_PATH_ENABLED = 'sales/cooling_off/enabled';
    private const string XML_PATH_EXCLUDE_ADMIN = 'sales/cooling_off/exclude_admin_orders';

    /**
     * Fixed cooling-off duration in minutes (not configurable)
     * SET TO 30 MINUTES
     */
    private const int COOLING_OFF_DURATION_MINUTES = 30;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    /**
     * Check if cooling-off period is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get cooling-off duration in minutes
     * Fixed at 30 minutes - not configurable
     *
     * @param int|null $storeId
     * @return int
     */
    public function getDurationMinutes(?int $storeId = null): int
    {
        return self::COOLING_OFF_DURATION_MINUTES;
    }

    /**
     * Check if admin orders should be excluded
     *
     * @param int|null $storeId
     * @return bool
     */
    public function shouldExcludeAdminOrders(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EXCLUDE_ADMIN,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
