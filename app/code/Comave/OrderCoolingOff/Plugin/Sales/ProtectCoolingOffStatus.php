<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Plugin\Sales;

use Magento\Sales\Model\Order;
use Comave\OrderCoolingOff\Service\ValidationService;
use Psr\Log\LoggerInterface;

/**
 * Plugin to protect cooling-off orders from automatic status changes
 */
class ProtectCoolingOffStatus
{
    /**
     * @param ValidationService $validationService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ValidationService $validationService,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Prevent status changes during cooling-off period and store intended changes
     *
     * @param Order $subject
     * @param string|null $status
     * @return array
     */
    public function beforeSetStatus(Order $subject, ?string $status): array
    {
        // Handle null status gracefully - just ignore the operation
        if ($status === null) {
            $this->logger->info(
                'Null status passed to setStatus, ignoring operation',
                [
                    'order_id' => $subject->getEntityId(),
                    'current_status' => $subject->getStatus()
                ]
            );
            return [$subject->getStatus()];
        }

        if ($status === 'cooling_off') {
            return [$status];
        }

        if ($this->validationService->isTransitionAllowed()) {
            return [$status];
        }

        if ($this->validationService->isInCoolingOffPeriod($subject)) {
            $currentIntendedStatus = $subject->getData('intended_status_after_cooling_off');

            $this->logger->info(
                'ProtectCoolingOffStatus: Order in cooling-off period, checking intended status',
                [
                    'order_id' => $subject->getEntityId(),
                    'current_status' => $subject->getStatus(),
                    'requested_status' => $status,
                    'current_intended_status' => $currentIntendedStatus,
                    'will_update_intended' => ($currentIntendedStatus !== $status)
                ]
            );

            if ($currentIntendedStatus !== $status) {
                $subject->setData('intended_status_after_cooling_off', $status);

                $this->logger->info(
                    'ProtectCoolingOffStatus: Updated intended status',
                    [
                        'order_id' => $subject->getEntityId(),
                        'old_intended_status' => $currentIntendedStatus,
                        'new_intended_status' => $status
                    ]
                );
            }

            return [$subject->getStatus()];
        }

        return [$status];
    }

    /**
     * Prevent state changes during cooling-off period and store intended changes
     *
     * @param Order $subject
     * @param string|null $state
     * @return array
     */
    public function beforeSetState(Order $subject, ?string $state): array
    {
        // Handle null state gracefully - just ignore the operation
        if ($state === null) {
            $this->logger->info(
                'Null state passed to setState, ignoring operation',
                [
                    'order_id' => $subject->getEntityId(),
                    'current_state' => $subject->getState()
                ]
            );
            return [$subject->getState()];
        }

        if ($state === Order::STATE_NEW) {
            return [$state];
        }

        if ($this->validationService->isTransitionAllowed()) {
            return [$state];
        }

        if ($this->validationService->isInCoolingOffPeriod($subject)) {
            $currentIntendedState = $subject->getData('intended_state_after_cooling_off');

            $this->logger->info(
                'ProtectCoolingOffStatus: Order in cooling-off period, checking intended state',
                [
                    'order_id' => $subject->getEntityId(),
                    'current_state' => $subject->getState(),
                    'requested_state' => $state,
                    'current_intended_state' => $currentIntendedState,
                    'will_update_intended' => ($currentIntendedState !== $state)
                ]
            );

            if ($currentIntendedState !== $state) {
                $subject->setData('intended_state_after_cooling_off', $state);

                $this->logger->info(
                    'ProtectCoolingOffStatus: Updated intended state',
                    [
                        'order_id' => $subject->getEntityId(),
                        'old_intended_state' => $currentIntendedState,
                        'new_intended_state' => $state
                    ]
                );
            }

            return [$subject->getState()];
        }

        return [$state];
    }
}
