<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Comave\OrderCoolingOff\Service\ValidationService;
use Comave\OrderCoolingOff\Service\ConfigProvider;
use Psr\Log\LoggerInterface;

/**
 * Observer to set cooling-off status on order placement
 */
class SetCoolingOffStatus implements ObserverInterface
{
    /**
     * @param ValidationService $validationService
     * @param ConfigProvider $configProvider
     * @param DateTime $dateTime
     * @param LoggerInterface $logger
     * @param OrderRepositoryInterface $orderRepository
     */
    public function __construct(
        private readonly ValidationService $validationService,
        private readonly ConfigProvider $configProvider,
        private readonly DateTime $dateTime,
        private readonly LoggerInterface $logger,
        private readonly OrderRepositoryInterface $orderRepository
    ) {
    }

    /**
     * Set cooling-off status when order is placed
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        /** @var Order $order */
        $order = $observer->getEvent()->getOrder();

        if (!$order instanceof Order) {
            return;
        }

        if (!$this->validationService->shouldApplyCoolingOff($order)) {
            $this->logger->info(
                'Cooling-off period not applicable for order',
                [
                    'order_id' => $order->getEntityId(),
                    'reason' => 'validation_failed'
                ]
            );
            return;
        }

        try {
            $this->applyCoolingOffPeriod($order);
        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to apply cooling-off period',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'error' => $e->getMessage()
                ]
            );
        }
    }

    /**
     * Apply cooling-off period to order
     *
     * @param Order $order
     * @return void
     */
    private function applyCoolingOffPeriod(Order $order): void
    {
        $durationMinutes = $this->configProvider->getDurationMinutes((int) $order->getStoreId());

        $coolingOffUntil = new \DateTime();
        $coolingOffUntil->add(new \DateInterval("PT{$durationMinutes}M"));

        $currentStatus = $order->getStatus();
        $currentState = $order->getState();

        // Ensure we have valid fallback values if current status/state are null
        if ($currentStatus === null) {
            $currentStatus = 'processing';
            $this->logger->warning(
                'Order status was null when applying cooling-off, using fallback',
                [
                    'order_id' => $order->getEntityId(),
                    'fallback_status' => $currentStatus
                ]
            );
        }

        if ($currentState === null) {
            $currentState = Order::STATE_PROCESSING;
            $this->logger->warning(
                'Order state was null when applying cooling-off, using fallback',
                [
                    'order_id' => $order->getEntityId(),
                    'fallback_state' => $currentState
                ]
            );
        }

        $order->setStatus('cooling_off')
              ->setState(Order::STATE_NEW)
              ->setData('cooling_off_until', $coolingOffUntil->format('Y-m-d H:i:s'))
              ->setData('is_cooling_off_applied', 1)
              ->setData('intended_status_after_cooling_off', $currentStatus)
              ->setData('intended_state_after_cooling_off', $currentState);

        $order->addCommentToStatusHistory(
            __('Order placed with %1 minute cooling-off period. Customer can cancel until %2.', 
               $durationMinutes, 
               $coolingOffUntil->format('Y-m-d H:i:s')
            ),
            'cooling_off',
            false
        );

        $this->orderRepository->save($order);
    }
}
