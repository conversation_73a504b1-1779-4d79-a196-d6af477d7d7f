<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\OrderCoolingOff\Cron;

use Comave\OrderCoolingOff\Service\ProcessorService;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Cron job to process expired cooling-off orders every minute
 */
class ProcessExpiredOrders
{
    private ProcessorService $processorService;
    private OrderRepositoryInterface $orderRepository;
    private CollectionFactory $orderCollectionFactory;
    private DateTime $dateTime;
    private LoggerInterface $logger;

    public function __construct(
        ProcessorService $processorService,
        OrderRepositoryInterface $orderRepository,
        CollectionFactory $orderCollectionFactory,
        DateTime $dateTime,
        LoggerInterface $logger
    ) {
        $this->processorService = $processorService;
        $this->orderRepository = $orderRepository;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->dateTime = $dateTime;
        $this->logger = $logger;
    }

    /**
     * Process all expired cooling-off orders
     *
     * @return void
     */
    public function execute(): void
    {
        $this->logger->info('=== COOLING-OFF CRON JOB STARTED ===');

        try {
            $expiredOrders = $this->getExpiredCoolingOffOrders();
            $processedCount = 0;

            $this->logger->info('Found expired cooling-off orders', [
                'count' => count($expiredOrders),
                'current_time' => $this->dateTime->gmtDate()
            ]);

            if (empty($expiredOrders)) {
                $this->logger->info('No expired cooling-off orders to process');
                return;
            }

            foreach ($expiredOrders as $order) {
                try {
                    $this->logger->info(
                        'ProcessExpiredOrders: Processing expired cooling-off order',
                        [
                            'order_id' => $order->getEntityId(),
                            'increment_id' => $order->getIncrementId(),
                            'current_status' => $order->getStatus(),
                            'current_state' => $order->getState(),
                            'cooling_off_until' => $order->getData('cooling_off_until'),
                            'intended_status_before_processing' => $order->getData('intended_status_after_cooling_off'),
                            'intended_state_before_processing' => $order->getData('intended_state_after_cooling_off')
                        ]
                    );

                    $this->processorService->transitionToAccepted($order);
                    $this->orderRepository->save($order);
                    $processedCount++;

                    $this->logger->info(
                        'Cooling-off order processed by cron job',
                        [
                            'order_id' => $order->getEntityId(),
                            'increment_id' => $order->getIncrementId()
                        ]
                    );

                } catch (\Exception $e) {
                    $this->logger->error(
                        'Failed to process cooling-off order',
                        [
                            'order_id' => $order->getEntityId(),
                            'error' => $e->getMessage()
                        ]
                    );
                }
            }

            if ($processedCount > 0) {
                $this->logger->info("Processed {$processedCount} expired cooling-off orders");
            }

        } catch (\Exception $e) {
            $this->logger->error(
                'Cooling-off orders processing cron job failed',
                [
                    'error' => $e->getMessage()
                ]
            );
        }
    }

    /**
     * Get orders with expired cooling-off period
     *
     * @return \Magento\Sales\Model\Order[]
     */
    private function getExpiredCoolingOffOrders(): array
    {
        $collection = $this->orderCollectionFactory->create();
        $collection->addFieldToFilter('status', 'cooling_off')
                   ->addFieldToFilter('cooling_off_until', ['notnull' => true])
                   ->addFieldToFilter('cooling_off_until', ['lt' => $this->dateTime->gmtDate()]);

        return $collection->getItems();
    }
}
