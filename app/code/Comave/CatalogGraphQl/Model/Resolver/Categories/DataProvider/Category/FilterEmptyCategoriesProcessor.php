<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Categories\DataProvider\Category;

use Magento\Catalog\Model\ResourceModel\Category\Collection;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\DB\Select;
use Magento\GraphQl\Model\Query\ContextInterface;
use Magento\InventoryApi\Api\Data\SourceItemInterface;

class FilterEmptyCategoriesProcessor
{
    /**
     * @param Collection $collection
     * @param SearchCriteriaInterface $searchCriteria
     * @param array $attributeNames
     * @param ContextInterface|null $context
     * @return Collection
     */
    public function process(
        Collection $collection,
        SearchCriteriaInterface $searchCriteria,
        array $attributeNames,
        ContextInterface $context = null
    ): Collection {
        $joinPart = $collection->getSelect()->getPart(Select::FROM);
        $catalogProductCategoryExists = array_search(
            'catalog_category_product',
            array_column($joinPart, 'tableName')
        );

        if (!$catalogProductCategoryExists) {
            $alias = 'ccp';
            $collection->getSelect()->join(
                [$alias => $collection->getTable('catalog_category_product')],
                "e.entity_id = $alias.category_id",
                []
            );
        } else {
            $alias = array_keys($joinPart)[$catalogProductCategoryExists];
        }

        $collection->getSelect()
            ->join(
                ['stock' => $collection->getTable('cataloginventory_stock_status')],
                "$alias.product_id = stock.product_id AND stock.stock_status = " . SourceItemInterface::STATUS_IN_STOCK,
                []
            )
            ->group('e.entity_id');

        return $collection;
    }
}
