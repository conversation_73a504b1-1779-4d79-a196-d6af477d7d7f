<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\Product;

use Comave\Catalog\Api\Data\Product\BlacklistSearchResultInterface;
use Comave\Catalog\Api\Data\Product\BlacklistSearchResultInterfaceFactory;
use Comave\Catalog\Api\Product\BlacklistListRepositoryInterface;
use Comave\Catalog\Model\ResourceModel\Product\Blacklist\Collection;
use Comave\Catalog\Model\ResourceModel\Product\Blacklist\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

class BlacklistListRepository implements BlacklistListRepositoryInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     * @param \Comave\Catalog\Api\Data\Product\BlacklistSearchResultInterfaceFactory $searchResultFactory
     * @param \Comave\Catalog\Model\ResourceModel\Product\Blacklist\CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly BlacklistSearchResultInterfaceFactory $searchResultFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return BlacklistSearchResultInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria): BlacklistSearchResultInterface
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var BlacklistSearchResultInterface $searchResult */
        $searchResult = $this->searchResultFactory->create();
        $searchResult->setSearchCriteria($searchCriteria);
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResult->setTotalCount($collection->getSize());
        $searchResult->setItems($collection->getItems());

        return $searchResult;
    }
}
