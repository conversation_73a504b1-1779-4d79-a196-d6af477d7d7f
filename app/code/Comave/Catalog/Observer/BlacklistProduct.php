<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Observer;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Model\Product\BlacklistUiManager;
use Exception;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class BlacklistProduct implements ObserverInterface
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly BlacklistUiManager $blacklistUiManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            $product = $observer->getProduct();
            $blacklistedProduct = $this->blacklistUiManager->getBySku($product->getSku());
            $blacklistedProduct->setProductSku($product->getSku());
            $blacklistedProduct->setProductStatus(BlacklistInterface::PRODUCT_STATUS_UNTRACKED);
            $this->blacklistUiManager->save($blacklistedProduct);
        } catch (Exception|LocalizedException|NoSuchEntityException|CouldNotSaveException $exception) {
            $this->logger->warning('Could not save blacklist product.', [
                'reason' => $exception->getMessage(),
            ]);
        }
    }
}
