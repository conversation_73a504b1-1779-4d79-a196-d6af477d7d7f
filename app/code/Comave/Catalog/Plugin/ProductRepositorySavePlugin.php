<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Plugin;

use Closure;
use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Exception\StateException;

class ProductRepositorySavePlugin
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     */
    public function __construct(
        private readonly BlacklistUiManager $blacklistUiManager,
    ) {
    }

    /**
     * @param ProductRepository $subject
     * @param ProductInterface $product
     * @return array|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function beforeSave(
        ProductRepository $subject,
        ProductInterface $product,
    ): ?array {
        if ($this->blacklistUiManager->getBySku($product->getSku())->getId()) {
            throw new StateException(__('Product is blacklisted.'));
        }

        return null;
    }
}
