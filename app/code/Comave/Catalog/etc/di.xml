<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\Catalog\Api\Data\Product\BlacklistInterface"
                type="Comave\Catalog\Model\Product\Blacklist"/>
    <preference for="Comave\Catalog\Api\Data\Product\BlacklistSearchResultInterface"
                type="Comave\Catalog\Model\Product\BlacklistSearchResults"/>
    <preference for="Comave\Catalog\Api\Product\BlacklistRepositoryInterface"
                type="Comave\Catalog\Model\Product\BlacklistRepository"/>
    <preference for="Comave\Catalog\Api\Product\BlacklistListRepositoryInterface"
                type="Comave\Catalog\Model\Product\BlacklistListRepository"/>
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\Catalog\Api\Data\Product\BlacklistInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_catalog_product_blacklist</item>
                    <item name="identifierField" xsi:type="string">blacklist_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Model\ResourceModel\Product\Blacklist">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">
                Comave\Catalog\Api\Data\Product\BlacklistInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\Catalog\Model\ResourceModel\Product\Blacklist\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_catalog_product_blacklist</argument>
            <argument name="model" xsi:type="string">Comave\Catalog\Model\Product\Blacklist</argument>
            <argument name="resourceModel" xsi:type="string">
                Comave\Catalog\Model\ResourceModel\Product\Blacklist
            </argument>
            <argument name="idFieldName" xsi:type="string">blacklist_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_catalog_product_blacklist</argument>
            <argument name="eventObject" xsi:type="string">comave_catalog_product_blacklist</argument>
            <argument name="interfaceClass" xsi:type="string">
                Comave\Catalog\Api\Data\Product\BlacklistInterface
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="comave_catalog_product_blacklisting_data_source" xsi:type="string">ComaveCatalogProductBlacklistGridCollection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="ComaveCatalogProductBlacklistGridCollection" type="Comave\Catalog\Model\ResourceModel\Product\Blacklist\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_catalog_product_blacklist</argument>
            <argument name="model" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\Document</argument>
            <argument name="resourceModel" xsi:type="string">Comave\Catalog\Model\ResourceModel\Product\Blacklist</argument>
            <argument name="idFieldName" xsi:type="string">blacklist_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_catalog_product_blacklist_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_catalog_product_blacklist_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\Catalog\Api\Data\Product\BlacklistInterface</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Catalog\Model\ProductRepository">
        <plugin name="comave_catalog_product_repository_save" type="Comave\Catalog\Plugin\ProductRepositorySavePlugin" />
    </type>
    <!-- @todo - this needs refactoring or removed entirely -->
<!--    <type name="Magento\Catalog\Model\Product">-->
<!--        <plugin name="comave_catalog_product_variant_image_inheritance"-->
<!--                type="Comave\Catalog\Plugin\ProductVariantImageInheritancePlugin"-->
<!--                sortOrder="10"/>-->
<!--    </type>-->
    <type name="Magento\Catalog\Model\Category\DataProvider">
        <plugin name="comave_catalog_category_data_provider" type="Comave\Catalog\Plugin\Category\DataProvider" sortOrder="10"/>
    </type>

    <type name="Comave\Catalog\Controller\Adminhtml\Category\Image\Upload">
        <arguments>
            <argument name="orientationValidator" xsi:type="object">
                Comave\Catalog\Model\Category\Image\OrientationValidator
            </argument>
        </arguments>
    </type>
</config>
