<?php

declare(strict_types=1);

namespace Comave\ClubGraphQl\GraphQl\Filter;

use Magento\Catalog\Model\ResourceModel\Category\Collection;
use Magento\Framework\Api\Filter;
use Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor\CustomFilterInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\DB\Select;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;

class Clubs implements CustomFilterInterface
{
    /**
     * @param Filter $filter
     * @param AbstractDb $collection
     * @return bool
     */
    public function apply(Filter $filter, AbstractDb $collection): bool
    {
        if (!$collection instanceof Collection) {
            return false;
        }

        if (empty($filter->getValue())) {
            throw new GraphQlInputException(
                __(
                    'The "%1" filter must have a value and not be empty',
                    (string) __("Club")
                )
            );
        }

        $joinPart = $collection->getSelect()->getPart(Select::FROM);
        $catalogProductCategoryExists = array_search(
            'catalog_category_product',
            array_column($joinPart, 'tableName')
        );

        if (!$catalogProductCategoryExists) {
            $hasRowId = $collection->getConnection()->tableColumnExists(
                $collection->getConnection()->getTableName('catalog_category_entity'),
                'row_id'
            );
            $alias = 'ccp';
            $collection->getSelect()->join(
                ['cpc' => $collection->getConnection()->getTableName('catalog_category_product')],
                sprintf('cpc.category_id = e.%s', $hasRowId ? 'row_id' : 'entity_id'),
                []
            );
        } else {
            $alias = array_keys($joinPart)[$catalogProductCategoryExists];
        }

        $collection->getSelect()
           ->join(
                ['ccp' => $collection->getConnection()->getTableName('comave_club_product')],
                "$alias.product_id = ccp.product_id",
                []
            )->join(
                ['cc' => $collection->getConnection()->getTableName('comave_club')],
                'cc.club_id = ccp.club_id',
                []
            )->where(
                $filter->getConditionType() === 'in' ?
                    'cc.uniqueid IN (?)' :
                    'cc.uniqueid = ?',
                $filter->getValue()
            )->group('cpc.category_id');

        return true;
    }
}
