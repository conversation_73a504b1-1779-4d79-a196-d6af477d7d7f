define([
    'jquery',
    'mage/translate',
    'Comave_PhoneValidation/js/phone-validator'
], function ($, $t, phoneValidator) {
    'use strict';

    /**
     * Phone Input Widget
     * @param {Object} config
     * @param {HTMLElement|jQuery} element
     * @constructor
     */
    function PhoneInput(config, element) {
        this.config = $.extend({
            initialCountry: 'auto',
            preferredCountries: ['us', 'gb'],
            separateDialCode: true,
            nationalMode: false,
            autoPlaceholder: 'polite'
        }, config);
        
        this.$originalInput = $(element);
        
        // Only initialize if intlTelInput is available
        if (window.intlTelInput) {
            this.init();
        } else {
            console.warn('intlTelInput library not loaded - phone validation will not be enhanced');
        }
    }

    PhoneInput.prototype = {
        /**
         * Initialize the phone input
         */
        init: function () {
            if (!this.$originalInput.length || this.$originalInput.data('intlAttached')) {
                return;
            }

            this.createDisplayInput();
            
            // Only proceed if intlTelInput initialization is successful
            if (this.initializeIntlTelInput()) {
                this.bindEvents();
                this.setupValidation();
                this.sync();
                
                this.$originalInput.data('intlAttached', true);
            } else {
                // Fallback: remove the display input and show the original
                this.$displayInput.remove();
                this.$errorElement.remove();
                this.$originalInput.css({ position: '', left: '' });
                console.warn('Phone input enhancement failed - using regular input');
            }
        },

        /**
         * Create the display input that will replace the original
         */
        createDisplayInput: function () {
            const uniqueId = this.generateUniqueId();
            
            this.$displayInput = $('<input>', {
                type: 'tel',
                id: 'display_' + uniqueId,
                class: 'admin__control-text admin-intl-phone input-text',
                'data-validate': '{"validate-intl-phone-admin": true}'
            }).insertBefore(this.$originalInput);

            this.$displayInput.val(this.$originalInput.val());
            this.$originalInput.css({ position: 'absolute', left: '-9999px' });

            this.$errorElement = $('<div>', {
                class: 'phone-error-msg',
                css: { display: 'none', color: '#c00', marginTop: '4px' }
            }).insertAfter(this.$displayInput);
        },

        /**
         * Initialize the international telephone input library
         */
        initializeIntlTelInput: function () {
            if (!window.intlTelInput) {
                console.warn('intlTelInput library not loaded');
                return false;
            }

            try {
                this.iti = window.intlTelInput(this.$displayInput[0], {
                    initialCountry: this.config.initialCountry,
                    preferredCountries: this.config.preferredCountries,
                    separateDialCode: this.config.separateDialCode,
                    dropdownContainer: document.body,
                    nationalMode: this.config.nationalMode,
                    autoPlaceholder: this.config.autoPlaceholder
                });
                return true;
            } catch (error) {
                console.error('Failed to initialize intlTelInput:', error);
                return false;
            }
        },

        /**
         * Bind necessary events
         */
        bindEvents: function () {
            const self = this;

            // Sync on country change, blur, and keyup
            this.$displayInput.on('countrychange blur keyup', function () {
                self.sync();
                self.validate();
            });

            // Sync on form submit
            this.$displayInput.closest('form').on('submit', function () {
                self.sync();
            });
        },

        /**
         * Setup validation for the phone input
         */
        setupValidation: function () {
            const self = this;
            
            phoneValidator.addPhoneValidation(function () {
                return self.validatePhone();
            });
            
            this.$displayInput.addClass('validate-intl-phone-admin');
        },

        /**
         * Sync the display input value with the original input
         */
        sync: function () {
            if (this.iti) {
                const phoneNumber = this.iti.getNumber();
                this.$originalInput.val(phoneNumber).trigger('change');
            }
        },

        /**
         * Validate the phone number
         * @returns {boolean}
         */
        validatePhone: function () {
            const inputValue = this.$displayInput.val().trim();
            
            if (!inputValue) {
                this.hideError();
                return true;
            }

            if (!this.iti || !this.iti.isValidNumber()) {
                this.showError($t('Invalid phone number. Please check and try again.'));
                return false;
            }

            this.hideError();
            return true;
        },

        /**
         * Validate the current phone number (public method)
         * @returns {boolean}
         */
        validate: function () {
            return this.validatePhone();
        },

        /**
         * Show error message
         * @param {string} message
         */
        showError: function (message) {
            this.$errorElement.text(message).show();
        },

        /**
         * Hide error message
         */
        hideError: function () {
            this.$errorElement.hide();
        },

        /**
         * Generate unique ID for the display input
         * @returns {string}
         */
        generateUniqueId: function () {
            const name = this.$originalInput.attr('name');
            const id = this.$originalInput.attr('id');
            
            if (name) {
                return name.replace(/[\[\]]+/g, '_');
            }
            
            if (id) {
                return id;
            }
            
            return 'intl_tel_' + Date.now();
        }
    };

    /**
     * Widget factory function
     */
    return function (config, element) {
        return new PhoneInput(config, element);
    };
});
