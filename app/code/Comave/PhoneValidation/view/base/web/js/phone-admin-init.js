/**
 * Phone Validation Admin Initiator
 * 
 * This module follows proper separation of concerns:
 * - phone-validator.js: Handles jQuery validation method registration
 * - phone-input-widget.js: Core widget functionality for phone input enhancement
 * - phone-admin-init.js: DOM observation and widget initialization
 * 
 * Architecture:
 * 1. No nested require() calls within define()
 * 2. Each module has a single responsibility
 * 3. Clean dependency injection through AMD modules
 * 4. Proper error handling and validation separation
 */
define([
    'jquery',
    'Comave_PhoneValidation/js/phone-input-widget'
], function ($, PhoneInputWidget) {
    'use strict';

    const PHONE_SELECTORS = [
        'input[name$="[phone_no]"]',
        'input[name$="[default_phone_number]"]',
        'input[name="telephone"]',
        'input[name$="[telephone]"]',
        'input[name*="telephone"]',
        'input[name*="phone"]',
        'input[name*="mobile"]',
        'input.comave-intl-phone',
        'input[data-index="telephone"]',
        'input[data-ui-id*="telephone"]',
        'input[data-ui-id*="phone"]'
    ];

    /**
     * Phone Admin Initiator
     * Handles DOM observation and widget initialization
     */
    return function (config) {
        config = config || {};

        /**
         * Check if intlTelInput is available
         */
        function isIntlTelInputReady() {
            return typeof window.intlTelInput === 'function';
        }

        /**
         * Initialize phone widgets for existing inputs
         */
        function initializeExistingInputs(attempt = 1) {
            if (!isIntlTelInputReady()) {
                console.warn('intlTelInput not ready, retrying in 500ms...');
                setTimeout(() => initializeExistingInputs(attempt), 500);
                return;
            }

            const $inputs = $(PHONE_SELECTORS.join(',')).not('[data-intlAttached]');
            
            if ($inputs.length === 0 && attempt < 5) {
                // Retry if no inputs found and haven't exceeded max attempts
                setTimeout(() => initializeExistingInputs(attempt + 1), 1000);
                return;
            }
            
            console.log('Phone validation: Found ' + $inputs.length + ' phone inputs to enhance');
            
            $inputs.each(function () {
                PhoneInputWidget(config, this);
            });
        }

        /**
         * Observe DOM for new phone inputs
         */
        function observeDOM() {
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Check for new phone inputs in added nodes
                        $(mutation.addedNodes).find(PHONE_SELECTORS.join(',')).each(function () {
                            if (!$(this).data('intlAttached') && isIntlTelInputReady()) {
                                PhoneInputWidget(config, this);
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        /**
         * Initialize the phone validation system
         */
        function init() {
            $(function () {
                // Wait a bit to ensure all scripts are loaded
                setTimeout(function() {
                    initializeExistingInputs();
                    observeDOM();
                }, 100);
            });
        }

        // Start initialization
        init();
    };
});
