define(['jquery', 'intlTelInput'], function ($) {
    var intlTelInput = window.intlTelInput;

    return function (config, element) {
        var input = element;
        if (!input) return;

        var errorEl = document.getElementById('phone-error');

        var iti = intlTelInput(input, {
            initialCountry: config.initialCountry || 'auto',
            preferredCountries: config.preferred || ['us', 'gb'],
            separateDialCode: typeof config.separateDialCode !== 'undefined' ? config.separateDialCode : true,
            dropdownContainer: document.body,
            nationalMode: false,
            autoPlaceholder: 'polite'
        });

        function sync() {
            $('#contact_number').val(iti.getNumber());
        }

        function validatePhone() {
            const isValid = iti.isValidNumber();
            if (!input.value.trim()) {
                errorEl.style.display = 'none';
            } else if (!isValid) {
                errorEl.textContent = 'Invalid phone number. Please check and try again.';
                errorEl.style.display = 'block';
            } else {
                errorEl.style.display = 'none';
            }
            return isValid;
        }

        input.addEventListener('countrychange', function () {
            sync();
            validatePhone();
        });

        input.addEventListener('blur', function () {
            sync();
            validatePhone();
        });

        input.addEventListener('keyup', function () {
            sync();
            validatePhone();
        });

        $('form[data-role="form-profile-validate"]').on('submit', function (e) {
            if (!validatePhone()) {
                e.preventDefault();
            }
        });

        sync();
    };
});
