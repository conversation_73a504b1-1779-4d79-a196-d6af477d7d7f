define([
    'jquery',
    'mage/translate'
], function ($, $t) {
    'use strict';

    return {
        /**
         * Add international phone validation method
         * @param {Function} validationFunction - The validation function
         */
        addPhoneValidation: function (validationFunction) {
            // Wait for validator to be available
            require(['Magento_Ui/js/lib/validation/validator'], function () {
                if ($.validator && $.validator.addMethod) {
                    $.validator.addMethod(
                        'validate-intl-phone-admin',
                        validationFunction,
                        $t('Invalid phone number. Please check and try again.')
                    );
                }
            });
        }
    };
});
