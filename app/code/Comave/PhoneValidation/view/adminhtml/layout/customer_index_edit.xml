<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
      layout="admin-2columns-left">
    <head>
        <css src="Comave_PhoneValidation::intl/css/intlTelInput.min.css" />
        <script src="Comave_PhoneValidation::intl/intlTelInputWithUtils.min.js"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="comave.phone.validation.admin.init" template="Comave_PhoneValidation::admin/phone-init.phtml"/>
        </referenceContainer>
    </body>
</page>
