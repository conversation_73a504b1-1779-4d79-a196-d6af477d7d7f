<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Observer;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Service\Role;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\UrlInterface;
use Magento\LoginAsCustomerApi\Api\GetLoggedAsCustomerAdminIdInterface;
use Webkul\Marketplace\Helper\Data;

class CheckSellerStatus implements ObserverInterface
{
    /**
     * @var array|string[]
     */
    private array $allowedPaths = [
        'onboarding/',
        'mui/',
        'seller_payouts/',
        'closed',
        'rejected',
        'suspended',
        'deactivated',
        'closed',
        'underReview',
        'logout',
    ];

    /**
     * @param \Magento\LoginAsCustomerApi\Api\GetLoggedAsCustomerAdminIdInterface $getLoggedAsCustomerAdminId
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param \Magento\Framework\UrlInterface $urlBuilder
     * @param \Comave\SellerStatus\Service\Role $sellerRoleService
     */
    public function __construct(
        private readonly GetLoggedAsCustomerAdminIdInterface $getLoggedAsCustomerAdminId,
        private readonly Session $customerSession,
        private readonly Data $mpHelper,
        private readonly UrlInterface $urlBuilder,
        private readonly Role $sellerRoleService
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer): void
    {
        /** @var RequestInterface $request */
        $request = $observer->getRequest();

        if (
            $this->getLoggedAsCustomerAdminId->execute() !== 0 ||
            !$this->customerSession->isLoggedIn()
        ) {
            return;
        }

        if ($request->isAjax()) {
            return;
        }

        $isSeller = (bool)$this->mpHelper->isSeller();

        if ($isSeller === false) {
            return;
        }

        /** @var \Magento\Framework\HTTP\PhpEnvironment\Response $response */
        $response = $observer->getResponse();

        if ($response->isRedirect()) {
            return;
        }

        foreach ($this->allowedPaths as $path) {
            if (str_contains($request->getRequestUri(), $path)) {
                return;
            }
        }

        $userRole = $this->sellerRoleService->get(
            (int)($this->mpHelper->getCustomerId() ?: $this->customerSession->getCustomerId())
        );
        if (empty($userRole)) {
            $logoutUrl = $this->urlBuilder->getUrl('customer/account/logout');
            $response->setRedirect($logoutUrl);
            $request->setDispatched();

            return;
        }

        $roleActionInstance = $userRole->getExtensionAttributes()->getStatusInstance();

        if ($roleActionInstance instanceof ActionableInterface) {
            $roleActionInstance->act($response);
        }
    }
}
