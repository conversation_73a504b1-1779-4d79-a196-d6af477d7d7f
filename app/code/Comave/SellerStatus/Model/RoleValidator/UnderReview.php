<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\State;
use Magento\Framework\UrlInterface;

class UnderReview extends AbstractRoleValidator implements ActionableInterface, SelfAwareInterface
{
    public const string ROLE_NAME = 'Under Review';

    /**
     * @param State $appArea
     * @param UrlInterface $urlBuilder
     * @param RequestInterface $request
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(
        private readonly State $appArea,
        private readonly UrlInterface $urlBuilder,
        private readonly RequestInterface $request,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        $sellerCompany = $this->sellerCompanyProvider->get();
        $userRole = $this->companyUserRoleManagement->getRolesForCompanyUser(
            $sellerId,
            (int) $sellerCompany->getId()
        );

        $currentRole = empty($userRole) || current($userRole)->getRoleName() === 'Default User' ?
            $this->getRole() : null;

        return $currentRole === null ? null : $this;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function act(ResponseInterface $response): void
    {
        if ($this->appArea->getAreaCode() !== Area::AREA_FRONTEND) {
            return;
        }

        if ($response->isRedirect()) {
            return;
        }

        $response->setRedirect(
            $this->urlBuilder->getUrl('marketplace/account/underReview')
        );
        $this->request->setDispatched();
    }
}
