<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\SellerStatus\Service;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\Exception\LocalizedException;

class Role
{
    public function __construct(
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
    ) {
    }

    /**
     * @param int $sellerId
     * @return false|\Magento\Company\Api\Data\RoleInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get(int $sellerId): false|RoleInterface
    {
        if (empty($sellerId)) {
            throw new LocalizedException(__('Unable to determine seller, ID missing'));
        }

        return current(
            $this->companyUserRoleManagement->getRolesForCompanyUser(
                $sellerId,
                (int)$this->sellerCompanyProvider?->get()?->getId()
            )
        );
    }
}