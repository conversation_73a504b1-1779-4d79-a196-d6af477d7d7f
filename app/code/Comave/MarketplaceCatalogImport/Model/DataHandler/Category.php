<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\DataHandler;

use Comave\MarketplaceCatalogImport\Api\ProductDataHandlerInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\DB\Select;

class Category implements ProductDataHandlerInterface
{
    private const string KEY = 'category_ids';

    /**
     * @param CollectionFactory $categoryCollectionFactory
     */
    public function __construct(private readonly CollectionFactory $categoryCollectionFactory)
    {
    }

    /**
     * @param ProductInterface $product
     * @param array $rowData
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function handle(ProductInterface $product, array $rowData): void
    {
        if (!isset($rowData[self::KEY]) || empty($rowData[self::KEY])) {
            return;
        }

        $product->unsetData(self::KEY);

        foreach (explode('|', $rowData[self::KEY]) as $category) {
            if (is_numeric($category)) {
                $product->setData(
                    self::KEY,
                    explode('|', $rowData[self::KEY])
                );

                return;
            }
        }

        $collection = $this->categoryCollectionFactory->create();
        $collection->getSelect()
            ->reset(Select::COLUMNS)
            ->columns(['entity_id']);
        $collection->addAttributeToSelect('category_code', 'inner');
        $collection->addFieldToFilter(
            'category_code',
            [
                'in' => array_map(
                    [$this, 'sanitizeCategoryCode'],
                    explode('|', $rowData[self::KEY])
                )
            ]
        );

        if (!$collection->getSize()) {
            return;
        }

        $product->setData(self::KEY, $collection->getAllIds());
    }

    /**
     * @param string $category
     * @return string
     */
    public function sanitizeCategoryCode(string $category): string
    {
        return html_entity_decode($category, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}
