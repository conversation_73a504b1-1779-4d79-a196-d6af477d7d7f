<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

use Magento\Catalog\Model\Product;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;

class AttributeValidator
{
    private ?array $loadedAttributes = null;

    /**
     * @param AttributeOptionManagementInterface $attributeOptionManagement
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly AttributeOptionManagementInterface $attributeOptionManagement,
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param array $attributeCodes
     * @return void
     */
    public function loadAttributes(array $attributeCodes): void
    {
        $connection = $this->resourceConnection->getConnection('read');
        $selectAttributes = $connection->select()
            ->from(
                ['e' => $this->resourceConnection->getTableName('eav_attribute')],
                [
                    'attribute_code',
                    'backend_type',
                    'source_model'
                ]
            )->where(
                'attribute_code IN (?)',
                $attributeCodes
            );

        $this->loadedAttributes = $connection->fetchAssoc($selectAttributes);
    }

    /**
     * @param mixed $value
     * @param string $attributeCode
     * @return bool
     * @throws LocalizedException
     */
    public function validateAttributeValue(mixed &$value, string $attributeCode): bool
    {
        if (!isset($this->loadedAttributes[$attributeCode])) {
            return true;
        }

        if ($attributeCode === 'type') {
            return in_array($value, ['simple', 'configurable']);
        }

        if ($attributeCode === 'quantity_and_stock_status') {
            return is_numeric($value);
        }

        if (!empty($this->loadedAttributes[$attributeCode]['source_model'])) {
            $options = $this->attributeOptionManagement->getItems(
                Product::ENTITY,
                $attributeCode
            );

            $cleanValue = strtolower(strip_tags(trim($value)));

            foreach ($options as $option) {
                if (!empty($option->getStoreLabels())) {
                    foreach ($option->getStoreLabels() as $storeLabel) {
                        $cleanLabel = strtolower(strip_tags(trim($storeLabel)));

                        if (!str_contains($cleanLabel, $cleanValue)) {
                            continue;
                        }

                        $value = $option->getValue();

                        return true;
                    }
                }

                $cleanLabel = strtolower(strip_tags(trim($option->getLabel())));

                if (!str_contains($cleanLabel, $cleanValue)) {
                    continue;
                }

                $value = $option->getValue();

                return true;
            }

            return false;
        }

        $attributeType = $this->loadedAttributes[$attributeCode]['backend_type'];

        return match($attributeType) {
            'decimal' => is_numeric($value) && (float) $value,
            'int', 'integer' => is_numeric($value) && (int) $value,
            'datetime' => strtotime($value) !== false,
            default => strlen($value) > 0,
        };
    }
}
