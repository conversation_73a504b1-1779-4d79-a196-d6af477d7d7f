<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface;
use Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterfaceFactory;
use Comave\MarketplaceCatalogImport\Api\ImportEntityRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Webkul\Marketplace\Model\Seller;

class ImportProcess
{
    /**
     * @param ImportEntityRepositoryInterface $importEntityRepository
     * @param ImportEntityInterfaceFactory $importFactory
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly ImportEntityRepositoryInterface $importEntityRepository,
        private readonly ImportEntityInterfaceFactory $importFactory,
        private readonly ResourceConnection $resourceConnection,
    ) {
    }

    /**
     * @param Seller $seller
     * @param ExportEntityInterface $exportEntity
     * @param array $rowsToImport
     * @return int
     */
    public function execute(Seller $seller, ExportEntityInterface $exportEntity, array $rowsToImport): int
    {
        /** @var ImportEntityInterface $importEntity */
        $importEntity = $this->importFactory->create();
        $importEntity->setTemplateEntityId(
            (int) $exportEntity->getId()
        )->setSellerId((int) $seller->getSellerId());

        $this->importEntityRepository->save($importEntity);

        $connection = $this->resourceConnection->getConnection('write');
        $rows = [];
        $columnData = $rowsToImport[0];
        array_shift($rowsToImport);

        foreach ($rowsToImport as $row) {
            $rows[] = [
                'import_id' => $importEntity->getId(),
                'column_data' => json_encode(array_map([$this, 'sanitizeCsvField'], $columnData)),
                'data' => json_encode(array_map([$this, 'sanitizeCsvField'], $row))
            ];
        }

        $connection->insertMultiple(
            $connection->getTableName('comave_product_import_rows'),
            $rows
        );

        return (int) $importEntity->getId();
    }

    /**
     * @param string $field
     * @return string
     */
    public function sanitizeCsvField(string $field): string
    {
        if (empty($field)) {
            return $field;
        }

        $field = trim($field);
        $field = strip_tags($field);
        $field = htmlspecialchars($field, ENT_QUOTES);

        return preg_replace('/[\x00-\x1F\x7F]/u', '', $field);
    }
}
