<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

use Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface;
use Comave\MarketplaceCatalogImportAdminUi\Model\Config\Source\Requirements;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\ResponseInterface;

class TemplateDownloader
{
    public const array REQUIRED_ATTRIBUTES = [
        'sku',
        'type',
        'ean',
        'name',
        'brand',
        'price',
        'quantity_and_stock_status',
        'weight'
    ];

    public function __construct(
        private readonly FileFactory $fileFactory
    ) {
    }

    /**
     * @param ExportEntityInterface $exportEntity
     * @param bool $asString
     * @return ResponseInterface|string
     * @throws \Exception
     */
    public function execute(ExportEntityInterface $exportEntity, bool $asString = false): ResponseInterface|string
    {
        /** @var resource $stream */
        $stream = fopen('php://temp', 'rb+'); //phpcs:ignore
        $formattedHeaders = [];

        foreach (self::REQUIRED_ATTRIBUTES as $attributeCode) {
            $formattedHeaders[] = sprintf(
                '%s (%s)',
                $attributeCode,
                'required'
            );
        }

        foreach ($exportEntity->getAttributeConfiguration()['attribute_config'] as $attributeRow) {
            if (in_array($attributeRow['attribute_code'], self::REQUIRED_ATTRIBUTES)) {
                continue;
            }

            $formattedHeaders[] = sprintf(
                '%s (%s)',
                $attributeRow['attribute_code'],
                (int) $attributeRow['requirement'] === Requirements::OPTIONAL ?
                    'optional' : 'required'
            );
        }

        fputcsv($stream, $formattedHeaders); //phpcs:ignore
        rewind($stream);

        return $asString ?
            (string) stream_get_contents($stream) :
            $this->fileFactory->create(
                $exportEntity->getName() . '_' . time() . '.csv',
                [
                    'type' => 'string',
                    'value' => (string) stream_get_contents($stream),
                    'rm' => true,
                ],
                DirectoryList::TMP,
                'text/csv',
            );
    }

    /**
     * @return array|array[]
     */
    public function getMinimumRequiredAttributes(): array
    {
        $minimumRequiredData = [
            'attribute_config' => []
        ];

        foreach (TemplateDownloader::REQUIRED_ATTRIBUTES as $attributeCode) {
            $minimumRequiredData['attribute_config'][] = [
                'attribute_code' => $attributeCode,
                'requirement' => 1
            ];
        }

        return $minimumRequiredData;
    }
}
