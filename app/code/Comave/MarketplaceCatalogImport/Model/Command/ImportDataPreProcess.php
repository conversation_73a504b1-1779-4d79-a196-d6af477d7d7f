<?php

declare(strict_types=1);

namespace Comave\MarketplaceCatalogImport\Model\Command;

class ImportDataPreProcess
{
    /**
     * @param array $importingRows
     * @return array[]
     * @throws \JsonException
     */
    public function preprocess(array $importingRows): array
    {
        $formattedData = [];

        foreach ($importingRows as $productRow) {
            $columnData = json_decode(
                $productRow['column_data'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $decodedProductData = json_decode(
                $productRow['data'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $formattedData[] = array_combine($columnData, $decodedProductData);
        }

        $result = [];

        foreach ($formattedData as $formattedRow) {
            $result[$formattedRow['sku']] = $formattedRow;

            if ($formattedRow['type'] !== 'configurable') {
                continue;
            }

            $result[$formattedRow['sku']]['children'] = [];
            preg_match_all(
                '/sku=([^,|]+)/',
                $formattedRow[ConfigurableProductValidator::KEY_PATH],
                $matches
            );

            foreach ($matches[1] as $childSku) {
                $foundSku = array_search(
                    $childSku,
                    array_column($formattedData, 'sku')
                );

                $result[$formattedRow['sku']]['children'][$childSku] = $formattedData[$foundSku];
                unset($result[$childSku]);
            }
        }

        return $result;
    }
}
