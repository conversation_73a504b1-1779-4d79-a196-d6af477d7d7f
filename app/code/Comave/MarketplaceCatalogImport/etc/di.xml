<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\MarketplaceCatalogImport\Api\Data\ExportEntityInterface"
                type="Comave\MarketplaceCatalogImport\Model\ExportEntity"/>
    <preference for="Comave\MarketplaceCatalogImport\Api\ExportEntityRepositoryInterface"
                type="Comave\MarketplaceCatalogImport\Model\ExportEntityRepository"/>
    <preference for="Comave\MarketplaceCatalogImport\Api\Data\ImportEntityInterface"
                type="Comave\MarketplaceCatalogImport\Model\ImportEntity"/>
    <preference for="Comave\MarketplaceCatalogImport\Api\ImportEntityRepositoryInterface"
                type="Comave\MarketplaceCatalogImport\Model\ImportEntityRepository"/>
</config>
