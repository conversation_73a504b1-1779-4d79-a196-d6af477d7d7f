<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\RateProviders;

use Coditron\CustomShippingRate\Api\ShippingRateProviderInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as TableRatesFactory;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Psr\Log\LoggerInterface;

class RatesByService implements ShippingRateProviderInterface
{
    /**
     * @param TableRatesFactory $ratesCollectionFactory
     * @param LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        private readonly TableRatesFactory $ratesCollectionFactory,
        private readonly LoggerInterface $logger,
        private readonly array $data = []
    ) {
    }

    /**
     * @param RateRequest $request
     * @param string $sellerIdentifier
     * @param string $sellerId
     * @return array<ShipTableRates[]>
     */
    public function get(RateRequest $request, string $sellerIdentifier, string $sellerId): array
    {
        /** @var Collection $tableRatesCollection */
        $tableRatesCollection = $this->ratesCollectionFactory->create()
            ->addFieldToFilter('seller_id', ['eq' => $sellerId])
            ->addFieldToFilter('weight', ['gteq' => $this->data['total_weight']])
            ->addFieldToFilter('countries', ['finset' => $this->data['country']])
            ->addFieldToFilter('service_type', ['in' => $this->data['services']]) // Fetch all service types at once
            ->setOrder('weight', 'ASC');

        $this->logger->info(
            '[CoditronFetchRates] Fetching rates select',
            [
                'query' => $tableRatesCollection->getSelect()->__toString(),
                'seller' => $sellerId,
            ]
        );

        $tableRatesByServiceType = [];

        if (!$tableRatesCollection->getSize()) {
            return $tableRatesByServiceType;
        }

        /** @var ShipTableRates $rate */
        foreach ($tableRatesCollection->getItems() as $rate) {
            if (!isset($tableRatesByServiceType[$rate->getServiceType()])) {
                $tableRatesByServiceType[$rate->getServiceType()] = [];
            }
            $tableRatesByServiceType[$rate->getServiceType()][] = $rate;
        }

        return $tableRatesByServiceType;
    }
}
