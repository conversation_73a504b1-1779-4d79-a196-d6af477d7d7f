<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model;

use Coditron\CustomShippingRate\Model\Command\ProductDataBuilder;
use Coditron\CustomShippingRate\Model\RateProviders\RatePoolProvider;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Backend\App\Area\FrontNameResolver;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Quote\Model\Quote\Address\RateResult\Error;
use Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory;
use Magento\Quote\Model\Quote\Address\RateResult\MethodFactory;
use Magento\Shipping\Model\Carrier\AbstractCarrier;
use Magento\Shipping\Model\Carrier\CarrierInterface;
use Magento\Shipping\Model\Rate\Result;
use Magento\Shipping\Model\Rate\ResultFactory;
use Coditron\CustomShippingRate\Helper\Data;
use Webkul\Marketplace\Service\UserInfoService;

use Psr\Log\LoggerInterface;

class Carrier extends AbstractCarrier implements CarrierInterface
{
    public const string CODE = 'customshippingrate';

    /**
     * Code of the carrier
     *
     * @var string
     */
    protected $_code = self::CODE;

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param ErrorFactory $rateErrorFactory
     * @param LoggerInterface $logger
     * @param ResultFactory $rateFactory
     * @param MethodFactory $rateMethodFactory
     * @param State $state
     * @param Data $customShippingRateHelper
     * @param \Magento\Directory\Model\CurrencyFactory $currencyFactory
     * @param ProductDataBuilder $productDataBuilder
     * @param RatePoolProvider $ratePoolProvider
     * @param array $data
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        ErrorFactory $rateErrorFactory,
        LoggerInterface $logger,
        private readonly ResultFactory $rateFactory,
        private readonly MethodFactory $rateMethodFactory,
        private readonly State $state,
        private readonly Data $customShippingRateHelper,
        private readonly \Magento\Directory\Model\CurrencyFactory $currencyFactory,
        private readonly ProductDataBuilder $productDataBuilder,
        private readonly RatePoolProvider $ratePoolProvider,
        private readonly UserInfoService $userInfoService,
        array $data = []
    ) {
        parent::__construct($scopeConfig, $rateErrorFactory, $logger, $data);
    }

    /**
     * Collect and get rates
     *
     * @param RateRequest $request
     * @return Collection|Result|bool|Error
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function collectRates(RateRequest $request): Collection|Result|bool|Error
    {
        $this->_logger->info(
            '[CoditronRates] Starting custom seller rate analysis',
        );

        if (!$this->canCollectRates()) {
            $error = $this->getErrorMessage();
            $this->_logger->notice(
                '[CoditronRates] Cannot collect custom seller rates',
                [
                    'error' => $error instanceof Error ? $error->getErrorMessage() : 'N/A',
                ]
            );

            return $this->getErrorMessage();
        }

        if (!isset($request->getAllItems()[0])) {
            $this->_logger->warning(
                'CustomShippingRate encountered empty quote items in collectRates()',
                [
                    'request' => $request->getData()
                ]
            );

            return $this->rateFactory->create();
        }

        $quote = current($request->getAllItems())->getQuote();
        $this->_logger->info(
            '[CoditronRates] Collecting custom seller rates for quote',
            [
                'quote' => $quote->getId()
            ]
        );

        $items = $quote->getAllVisibleItems();
        $country = $request->getDestCountryId();
        $serviceTypeCodes = array_keys($this->getAllowedMethods());
        $toShipPerSellerArray = $this->productDataBuilder->build(
            $items,
            $country
        );
        $tableRatesByServiceType = [];
        $shippingResultPerSeller = [];
        $shippingResultPerServiceType = [];

        foreach ($toShipPerSellerArray as $sellerKey => $sellerData) {
            unset($sellerData['items']);
            [$sellerIdentifier, $sellerId] = explode('|', $sellerKey);

            $rateProvider = $this->ratePoolProvider->getBySeller(
                $sellerKey,
                $sellerData + ['services' => $serviceTypeCodes]
            );

            $tableRatesByServiceType[$sellerKey] = $rateProvider->get($request, $sellerIdentifier, $sellerId);
        }

        $this->_logger->info(
            '[CoditronRates] Collected custom seller rates from providers',
            [
                'quote' => $quote->getId(),
                'toShipPerSellerArray' => $toShipPerSellerArray,
                'tableRatesByServiceType' => $tableRatesByServiceType,
                'serviceTypes' => $serviceTypeCodes,
            ]
        );

        $sellerSubtotals = [];
        foreach ($tableRatesByServiceType as $sellerKey => $serviceTypes) {
            [, $sellerId] = explode('|', $sellerKey);

            if (!isset($sellerSubtotals[$sellerId])) {
                $sellerSubtotals[$sellerId] = $this->calculateSellerSubtotal($quote, (int)$sellerId);
            }
        }

        
        foreach ($serviceTypeCodes as $serviceTypeCode) {
            $totalPrice = 0;
            $minLeadTime = null;
            $maxLeadTime = null;
            $includedSellers = [];

            foreach ($toShipPerSellerArray as $sellerKey => $itemsData) {
                $selectedMethod = $this->selectBestShippingMethod($sellerKey, $serviceTypeCode, $tableRatesByServiceType, $quote);

                if (!$selectedMethod instanceof ShipTableRates) {
                    continue;
                }

                [, $sellerId] = explode('|', $sellerKey);
                $tableRate = $selectedMethod;

                $shippingPrice = $tableRate->getShippingPrice();
                $originalPrice = $shippingPrice;
                $freeShippingApplied = false;

                if ($tableRate->getFreeShipping() && $tableRate->getMinOrderAmount() > 0) {
                    $sellerSubtotal = $sellerSubtotals[$sellerId] ?? $this->calculateSellerSubtotal($quote, (int)$sellerId);

                    if ($sellerSubtotal >= $tableRate->getMinOrderAmount()) {
                        $shippingPrice = 0;
                        $freeShippingApplied = true;
                    }
                } elseif ($tableRate->getFreeShipping() && $tableRate->getMinOrderAmount() == 0) {
                    $shippingPrice = 0;
                    $freeShippingApplied = true;
                }

                $totalPrice += $shippingPrice;
                $minLeadTime = $minLeadTime === null ? $tableRate->getTotalLeadTime() : min($minLeadTime, $tableRate->getTotalLeadTime());
                $maxLeadTime = $maxLeadTime === null ? $tableRate->getTotalLeadTime() : max($maxLeadTime, $tableRate->getTotalLeadTime());
                $includedSellers[] = $sellerId;

                $shippingResultPerSeller[$sellerId][$serviceTypeCode] = [
                    'service_name' => $tableRate->getCourierName(),
                    'price' => $shippingPrice, 
                    'original_price' => $tableRate->getShippingPrice(),
                    'total_lead_time' => $tableRate->getTotalLeadTime(),
                    'items' => $itemsData['items'],
                    'total_weight' => $itemsData['total_weight'],
                    'free_shipping_applied' => $shippingPrice === 0 && $tableRate->getShippingPrice() > 0
                ];
            }

            if ($totalPrice > 0) {
                $shippingResultPerServiceType[$serviceTypeCode] = [
                    'price' => $totalPrice,
                    'min_total_lead_time' => $minLeadTime,
                    'max_total_lead_time' => $maxLeadTime,
                    'sellers' => $includedSellers   
                ];
            }
        }

        $this->_logger->info(
            '[CoditronRatesResults] Debugging Results',
            [
                'quote' => $quote->getId(),
                'shippingResultPerSeller' => $shippingResultPerSeller,
                'shippingResultPerServiceType' => $shippingResultPerServiceType
            ]
        );
        $sellerShip = json_encode($shippingResultPerSeller, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $quote->setShippingData($sellerShip);
        $result = $this->rateFactory->create();

        if (
            !$this->getConfigFlag('active') ||
            (!$this->isAdmin() && $this->hideShippingMethodOnFrontend())
        ) {
            return $result;
        }

        
        foreach ($this->getAllowedMethods() as $serviceTypeCode => $serviceTypeTitle) {
            if (!isset($shippingResultPerServiceType[$serviceTypeCode])) {
                $this->_logger->warning(
                    '[CoditronRatesResults] No Service Rate Found',
                    [
                        'quote' => $quote->getId(),
                        'serviceCode' => $serviceTypeCode
                    ]
                );

                continue;
            }

            $rate = $this->rateMethodFactory->create();
            $rate->setCarrier($this->_code);
            $rate->setCarrierTitle($this->getConfigData('title'));
            $rate->setMethod($serviceTypeCode);
            $rate->setMethodTitle($this->getTimeEnhancedMethodTitle(
                $serviceTypeTitle,
                $shippingResultPerServiceType[$serviceTypeCode]['min_total_lead_time'],
                $shippingResultPerServiceType[$serviceTypeCode]['max_total_lead_time']
            ));
            $rate->setCost($shippingResultPerServiceType[$serviceTypeCode]['price']);
            $rate->setPrice($shippingResultPerServiceType[$serviceTypeCode]['price']);
            $result->append($rate);
        }

        return $result;
    }

    /**
     * @param string $title
     * @param int $min
     * @param int $max
     * @return string
     */
    private function getTimeEnhancedMethodTitle(string $title, int $min, int $max): string
    {
        return $min !== $max ?
            $title . ' (' . $min . ' - ' . $max . ' days)' :
            $title . ' (' . $max . ' days)';
    }

    /**
     * Get allowed shipping methods
     *
     * @return array
     */
    public function getAllowedMethods(): array
    {
        $result = [];

        foreach ($this->customShippingRateHelper->getShippingType() as $shippingType) {
            $result[$shippingType['code']] = $shippingType['title'];
        }

        return $result;
    }

    public function isTrackingAvailable(): bool
    {
        return false;
    }

    /**
     * @param float $amount
     * @param string $currency
     * @param string $baseCurrency
     * @return float
     */
    public function convertPrice(float $amount,string $currency, string $baseCurrency): float
    {
        $rate = $this->currencyFactory->create()->load($currency)->getAnyRate($baseCurrency);

        return $amount * $rate;
    }

    /**
     * @return bool
     */
    protected function hideShippingMethodOnFrontend(): bool
    {
        return !$this->getConfigFlag('show_on_frontend');
    }

    /**
     * @return bool
     * @throws LocalizedException
     */
    protected function isAdmin(): bool
    {
        return $this->state->getAreaCode() == FrontNameResolver::AREA_CODE;
    }


    /**
     * Checks if shipping method can collect rates
     *
     * @return bool
     */
    public function canCollectRates(): bool
    {
        return (bool) $this->getConfigFlag('active') &&
            $this->customShippingRateHelper->isAllowedSellerTableRates();
    }

    /**
     * Calculate subtotal for a specific seller from quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @param int $sellerId
     * @return float
     */
    private function calculateSellerSubtotal(\Magento\Quote\Model\Quote $quote, int $sellerId): float
    {
        $subtotal = 0;
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            $productId = (int)$item->getProduct()->getId();
            $userInfo = $this->userInfoService->get($productId);

            if (!empty($userInfo['id']) && (int)$userInfo['id'] === $sellerId) {
                $subtotal += $item->getBaseRowTotal() - $item->getBaseDiscountAmount();
            }
        }

        return $subtotal;
    }

    /**
     * Select the best shipping method for a seller
     * Priority: 1) Free shipping with met threshold, 2) Least expensive shipping method
     *
     * @param string $sellerKey
     * @param string $serviceTypeCode
     * @param array $tableRatesByServiceType
     * @param \Magento\Quote\Model\Quote $quote
     * @return ShipTableRates|null
     */
    private function selectBestShippingMethod(
        string $sellerKey,
        string $serviceTypeCode,
        array $tableRatesByServiceType,
        \Magento\Quote\Model\Quote $quote
    ): ?ShipTableRates
    {
        if (!isset($tableRatesByServiceType[$sellerKey])) {
            return null;
        }

        [, $sellerId] = explode('|', $sellerKey);
        $sellerSubtotal = $this->calculateSellerSubtotal($quote, (int)$sellerId);
        $availableServiceTypes = $tableRatesByServiceType[$sellerKey];

        $allMethods = [];
        foreach ($availableServiceTypes as $serviceType => $methods) {
            $methodsArray = is_array($methods) ? $methods : [$methods];
            foreach ($methodsArray as $method) {
                if ($method instanceof ShipTableRates) {
                    $allMethods[] = $method;
                }
            }
        }

        $preferredServiceMethods = [];
        if (isset($availableServiceTypes[$serviceTypeCode])) {
            $preferredServiceMethods = is_array($availableServiceTypes[$serviceTypeCode])
                ? $availableServiceTypes[$serviceTypeCode]
                : [$availableServiceTypes[$serviceTypeCode]];
        }

        if (empty($preferredServiceMethods)) {
            $preferredServiceMethods = $allMethods;
        }

        if (empty($preferredServiceMethods)) {
            return null;
        }

        $freeShippingWithMetThreshold = null;
        $eligibleMethods = [];

        foreach ($preferredServiceMethods as $method) {
            if (!$method instanceof ShipTableRates) {
                continue;
            }

            if ($method->getFreeShipping()) {
                if ($method->getMinOrderAmount() > 0) {
                    $thresholdMet = $sellerSubtotal >= $method->getMinOrderAmount();
                    if ($thresholdMet && !$freeShippingWithMetThreshold) {
                        $freeShippingWithMetThreshold = $method;
                    }
                } else {
                    return $method;
                }
            } else {
                $eligibleMethods[] = $method;
            }
        }

        if ($freeShippingWithMetThreshold) {
            return $freeShippingWithMetThreshold;
        }

        if (!empty($eligibleMethods)) {
            $cheapestMethod = null;
            $lowestPrice = null;

            foreach ($eligibleMethods as $method) {
                $price = (float)$method->getShippingPrice();

                if ($lowestPrice === null || $price < $lowestPrice) {
                    $lowestPrice = $price;
                    $cheapestMethod = $method;
                }
            }

            return $cheapestMethod;
        }

        return null;
    }

    /**
     * Get error messages
     *
     * @return bool|Error
     */
    protected function getErrorMessage(): bool|Error
    {
        if (!$this->getConfigData('showmethod')) {
            return false;
        }

        /* @var DataObject|Error $error */
        $error = $this->_rateErrorFactory->create();
        $error->setCarrier($this->getCarrierCode());
        $error->setCarrierTitle($this->getConfigData('title'));
        $error->setErrorMessage($this->getConfigData('specificerrmsg'));

        return $error;
    }
}

